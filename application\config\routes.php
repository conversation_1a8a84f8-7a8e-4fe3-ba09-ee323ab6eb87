<?php
defined('BASEPATH') or exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'Landing';
$route['404_override'] = 'Page404';
$route['translate_uri_dashes'] = FALSE;

$route['maintenance'] = 'Landing/maintenance';
$route['contact'] = 'Landing/contact';
$route['privacypolicy'] = 'Landing/privacypolicy';
$route['termsofservice'] = 'Landing/termsofservice';

$route['auth/login'] = 'Auth/login';
$route['auth/login/process'] = 'Auth/process_login';

$route['auth/logout'] = 'Auth/logout';

$route['auth/register'] = 'Auth/register';

$route['auth/reset/password'] = 'Auth/reset_password';
$route['auth/reset/password/process'] = 'Auth/process_reset_password';

$route['auth/forgot'] = 'Auth/forgot';
$route['auth/forgot/process'] = 'Auth/process_forgot';

$route['dashboard'] = 'Dashboard';
$route['dashboard/monitoringmember/datatables'] = 'Dashboard/datatables_monitoring_member';
$route['dashboard/reportprofitomset'] = 'Dashboard/reportprofitomset';
$route['dashboard/reportprofitomset/datatables'] = 'Dashboard/datatables_reportprofitomset';
$route['dashboard/queuetransaction'] = 'Dashboard/queuetransaction';
$route['dashboard/queuetransaction/datatables'] = 'Dashboard/datatables_queuetransaction';
$route['dashboard/queuetransaction/cancel'] = 'Dashboard/process_cancel_transaction';
$route['dashboard/filter/reset'] = 'Dashboard/process_reset_filter';
$route['dashboard/filter/set'] = 'Dashboard/process_set_filter';
$route['dashboard/profitdetail'] = 'Dashboard/detailprofit';
$route['dashboard/historyerror'] = 'Dashboard/historyerror';
$route['dashboard/historyerror/datatables'] = 'Dashboard/datatables_historyerror';

$route['transaction/prabayar'] = 'Transaction/prabayar';
$route['transaction/prabayar/datatables'] = 'Transaction/datatables_prabayar';
$route['transaction/prabayar/detail/(:any)'] = 'Transaction/detail_prabayar/$1';
$route['transaction/prabayar/approve'] = 'Transaction/process_approve';
$route['transaction/prabayar/reject'] = 'Transaction/process_reject';
$route['transaction/prabayar/refund'] = 'Transaction/process_refund';
$route['transaction/prabayar/reorder'] = 'Transaction/reorder';

$route['transaction/pascabayar'] = 'Transaction/pascabayar';
$route['transaction/pascabayar/datatables'] = 'Transaction/datatables_pascabayar';
$route['transaction/pascabayar/detail/(:any)'] = 'Transaction/detail_pascabayar/$1';
$route['transaction/pascabayar/refund'] = 'Transaction/process_refund';

$route['transaction/smm'] = 'Transaction/smm';
$route['transaction/smm/datatables'] = 'Transaction/datatables_smm';
$route['transaction/smm/detail/(:any)'] = 'Transaction/detail_smm/$1';
$route['transaction/smm/refund'] = 'Transaction/process_refund';
$route['transaction/smm/approve'] = 'Transaction/process_approve';
$route['transaction/smm/reject'] = 'Transaction/process_reject';
$route['transaction/smm/reorder'] = 'Transaction/reorder';

$route['transaction/print/(:any)'] = 'Transaction/print/$1';
$route['transaction/print/(:any)/process'] = 'Transaction/process_print/$1';

$route['deposit/history'] = 'Deposit/history';
$route['deposit/history/datatables'] = 'Deposit/datatables_history';
$route['deposit/history/cancel'] = 'Deposit/process_cancel';
$route['deposit/history/pay/(:any)'] = 'Deposit/pay/$1';
$route['deposit/history/pay/finish/(:any)'] = 'Deposit/finish/$1';

$route['deposit/topup'] = 'Deposit/topup';
$route['deposit/topup/process'] = 'Deposit/process_topup';

$route['paymentgateway/manual'] = 'PaymentGateway/manual';
$route['paymentgateway/manual/datatables'] = 'PaymentGateway/datatables_manual';
$route['paymentgateway/manual/enable'] = 'PaymentGateway/process_enable';
$route['paymentgateway/manual/disable'] = 'PaymentGateway/process_disable';
$route['paymentgateway/manual/add'] = 'PaymentGateway/add';
$route['paymentgateway/manual/add/process'] = 'PaymentGateway/process_add';
$route['paymentgateway/manual/edit'] = 'PaymentGateway/edit';
$route['paymentgateway/manual/edit/process'] = 'PaymentGateway/process_edit';
$route['paymentgateway/manual/delete'] = 'PaymentGateway/process_delete';

$route['paymentgateway/automatic'] = 'PaymentGateway/automatic';
$route['paymentgateway/automatic/disable'] = 'PaymentGateway/process_disable_paymentgateway';
$route['paymentgateway/automatic/enable'] = 'PaymentGateway/process_enable_paymentgateway';
$route['paymentgateway/automatic/disablebcabot'] = 'PaymentGateway/process_disable_bcabot';
$route['paymentgateway/automatic/enablebcabot'] = 'PaymentGateway/process_enable_bcabot';
$route['paymentgateway/automatic/process/paymentgateway'] = 'PaymentGateway/process_change_paymentgateway';
$route['paymentgateway/automatic/process/bca'] = 'PaymentGateway/process_change_bca';
$route['paymentgateway/automatic/process/gopay'] = 'PaymentGateway/process_change_gopay';
$route['paymentgateway/automatic/process/ovo'] = 'PaymentGateway/process_change_ovo';
$route['paymentgateway/automatic/midtrans/enabledpayments'] = 'PaymentGateway/enabled_payments';
$route['paymentgateway/automatic/midtrans/enabledpayments/process'] = 'PaymentGateway/process_enabled_payments';
$route['paymentgateway/automatic/tripay/channelpayments'] = 'PaymentGateway/channel_payments';
$route['paymentgateway/automatic/tripay/channelpayments/process'] = 'PaymentGateway/process_channel_payments';
$route['paymentgateway/automatic/ipaymu/channelpayments'] = 'PaymentGateway/channel_payments';
$route['paymentgateway/automatic/ipaymu/channelpayments/process'] = 'PaymentGateway/process_channel_payments';
$route['paymentgateway/automatic/duitku/channelpayments'] = 'PaymentGateway/channel_payments_duitku';
$route['paymentgateway/automatic/duitku/channelpayments/process'] = 'PaymentGateway/process_channel_payments';
$route['paymentgateway/automatic/okeconnect/channelpayments'] = 'PaymentGateway/channel_payments';
$route['paymentgateway/automatic/okeconnect/channelpayments/process'] = 'PaymentGateway/process_channel_payments';
$route['paymentgateway/automatic/paydisini/channelpayments'] = 'PaymentGateway/channel_payments';
$route['paymentgateway/automatic/paydisini/channelpayments/process'] = 'PaymentGateway/process_channel_payments';
$route['paymentgateway/automatic/otp/gopay'] = 'PaymentGateway/process_get_otp_code';
$route['paymentgateway/automatic/login/gopay'] = 'PaymentGateway/process_login_gopay';
$route['paymentgateway/automatic/otp/ovo'] = 'PaymentGateway/process_get_otp_code_ovo';
$route['paymentgateway/automatic/login/ovo'] = 'PaymentGateway/process_login_ovo';
$route['paymentgateway/automatic/verify/ovo'] = 'PaymentGateway/process_verify_ovo';
$route['paymentgateway/automatic/feepayments'] = 'PaymentGateway/fee_payments';
$route['paymentgateway/automatic/feepayments/process'] = 'PaymentGateway/process_fee_payments';
$route['paymentgateway/automatic/buy'] = 'PaymentGateway/buy_notification_handler';
$route['paymentgateway/automatic/buy/process'] = 'PaymentGateway/process_buy_notification_handler';
$route['paymentgateway/automatic/configuration'] = 'PaymentGateway/configuration';
$route['paymentgateway/automatic/configuration/process'] = 'PaymentGateway/process_configuration';
$route['paymentgateway/automatic/notificationhandler/disable'] = 'PaymentGateway/disable_notification_handler';
$route['paymentgateway/automatic/notificationhandler/enable'] = 'PaymentGateway/enable_notification_handler';

$route['users/data'] = 'Users/Data';
$route['users/data/datatables'] = 'Users/Data/datatables_users';
$route['users/data/filter'] = 'Users/Data/filter';
$route['users/data/export'] = 'Users/Data/process_export_excel';
$route['users/data/change/pin'] = 'Users/Data/change_pin';
$route['users/data/change/pin/process'] = 'Users/Data/process_change_pin';
$route['users/data/change/password'] = 'Users/Data/change_password';
$route['users/data/change/password/process'] = 'Users/Data/process_change_password';
$route['users/data/add/user'] = 'Users/Data/add_user';
$route['users/data/add/user/process'] = 'Users/Data/process_add_user';
$route['users/data/delete'] = 'Users/Data/process_delete_user';
$route['users/data/deleted'] = 'Users/Data/deleted_user';
$route['users/data/deleted/datatables'] = 'Users/Data/datatables_deleted_user';
$route['users/data/deleted/restore'] = 'Users/Data/process_restore_user';
$route['users/data/decrease/balance'] = 'Users/Data/decrease_balance';
$route['users/data/decrease/balance/process'] = 'Users/Data/process_decrease_balance';
$route['users/data/change/role'] = 'Users/Data/change_role';
$route['users/data/change/role/process'] = 'Users/Data/process_change_role';
$route['users/data/verifikasiemail'] = 'Users/Data/process_verifikasi_email';
$route['users/data/history/balance/datatables'] = 'Users/Data/datatables_history_balance';
$route['users/data/history/transaction/ppob/datatables'] = 'Users/Data/datatables_history_transaction_ppob';
$route['users/data/history/transaction/smm/datatables'] = 'Users/Data/datatables_history_transaction_smm';
$route['users/data/history/login/datatables'] = 'Users/Data/datatables_history_login';
$route['users/data/history/login'] = 'Users/Data/history_login';
$route['users/data/detail/member/(:any)'] = 'Users/Data/detail_member_page/$1';

$route['users/transfer/saldo'] = 'Users/Transfer/saldo';
$route['users/transfer/saldo/datatables'] = 'Users/Transfer/datatables_transfer_saldo';
$route['users/transfer/saldo/process'] = 'Users/Transfer/process_transfer_saldo';

$route['users/deposit/history'] = 'Users/Deposit/history';
$route['users/deposit/history/datatables'] = 'Users/Deposit/datatables_history';
$route['users/deposit/history/get_statistics'] = 'Users/Deposit/get_statistics';
$route['users/deposit/history/filter'] = 'Users/Deposit/filter';
$route['users/deposit/history/confirm'] = 'Users/Deposit/process_confirm_topup';
$route['users/deposit/history/cancel'] = 'Users/Deposit/process_cancel_topup';
$route['users/deposit/history/wrong/nominal'] = 'Users/Deposit/wrong_nominal';
$route['users/deposit/history/wrong/nominal/process'] = 'Users/Deposit/process_wrong_nominal';

$route['users/ticket/history'] = 'Users/Ticket/history';
$route['users/ticket/history/datatables'] = 'Users/Ticket/datatables_history';
$route['users/ticket/history/close'] = 'Users/Ticket/process_close_ticket';
$route['users/ticket/history/conversation/(:any)'] = 'Users/Ticket/conversation/$1';
$route['users/ticket/history/conversation/(:any)/send'] = 'Users/Ticket/process_send_conversation/$1';

$route['users/verificationkyc'] = 'Users/Verificationkyc';
$route['users/verificationkyc/datatables'] = 'Users/Verificationkyc/datatables_verificationkyc';
$route['users/verificationkyc/process'] = 'Users/Verificationkyc/process_verificationkyc';
$route['users/verificationkyc/detail/(:any)'] = 'Users/Verificationkyc/detail/$1';

$route['admins/invoice/history'] = 'Admins/Invoice/history';
$route['admins/invoice/history/datatables'] = 'Admins/Invoice/datatables_history';
$route['admins/invoice/history/confirm'] = 'Admins/Invoice/process_confirm_topup';
$route['admins/invoice/history/cancel'] = 'Admins/Invoice/process_cancel_topup';
$route['admins/invoice/history/filter'] = 'Admins/Invoice/filter';

$route['admins/deposit/history'] = 'Admins/Deposit/history';
$route['admins/deposit/history/datatables'] = 'Admins/Deposit/datatables_history';
$route['admins/deposit/history/confirm'] = 'Admins/Deposit/process_confirm_topup';
$route['admins/deposit/history/cancel'] = 'Admins/Deposit/process_cancel_topup';
$route['admins/deposit/history/filter'] = 'Admins/Deposit/filter';
$route['admins/deposit/history/wrong/nominal'] = 'Admins/Deposit/wrong_nominal';
$route['admins/deposit/history/wrong/nominal/process'] = 'Admins/Deposit/process_wrong_nominal';

$route['admins/transfer/saldo'] = 'Admins/Transfer/saldo';
$route['admins/transfer/saldo/datatables'] = 'Admins/Transfer/datatables_transfer_saldo';
$route['admins/transfer/saldo/process'] = 'Admins/Transfer/process_transfer_saldo';

$route['product/prabayar'] = 'Product/prabayar';
$route['product/prabayar/datatables'] = 'Product/datatables_prabayar';
$route['product/prabayar/filter'] = 'Product/filter_prabayar';
$route['product/prabayar/add'] = 'Product/add_prabayar';
$route['product/prabayar/add/process'] = 'Product/process_add_prabayar';
$route['product/prabayar/edit/(:num)'] = 'Product/edit_prabayar/$1';
$route['product/prabayar/edit/(:num)/process'] = 'Product/process_edit_prabayar/$1';
$route['product/prabayar/delete'] = 'Product/process_delete_prabayar';
$route['product/prabayar/bulk/delete'] = 'Product/bulk_delete_prabayar';
$route['product/prabayar/bulk/delete/datatables'] = 'Product/datatables_bulk_delete_prabayar';
$route['product/prabayar/bulk/delete/process'] = 'Product/process_bulk_delete_prabayar';
$route['product/prabayar/bulk/delete/all'] = 'Product/bulk_delete_all_prabayar';
$route['product/prabayar/bulk/add'] = 'Product/bulk_add_prabayar';
$route['product/prabayar/bulk/add/select/category'] = 'Product/select_category_prabayar';
$route['product/prabayar/bulk/add/select/brand'] = 'Product/select_brand_prabayar';
$route['product/prabayar/bulk/add/data'] = 'Product/get_data_prabayar';
$route['product/prabayar/bulk/add/process'] = 'Product/process_bulk_add_prabayar';

$route['product/pascabayar'] = 'Product/pascabayar';
$route['product/pascabayar/datatables'] = 'Product/datatables_pascabayar';
$route['product/pascabayar/filter'] = 'Product/filter_pascabayar';
$route['product/pascabayar/bulk/delete'] = 'Product/bulk_delete_pascabayar';
$route['product/pascabayar/bulk/delete/datatables'] = 'Product/datatables_bulk_delete_pascabayar';
$route['product/pascabayar/bulk/delete/process'] = 'Product/process_bulk_delete_pascabayar';
$route['product/pascabayar/bulk/delete/all'] = 'Product/bulk_delete_all_pascabayar';
$route['product/pascabayar/bulk/add'] = 'Product/bulk_add_pascabayar';
$route['product/pascabayar/bulk/add/select/brand'] = 'Product/select_brand_pascabayar';
$route['product/pascabayar/bulk/add/data'] = 'Product/get_data_pascabayar';
$route['product/pascabayar/bulk/add/process'] = 'Product/process_bulk_add_pascabayar';

$route['product/smm'] = 'Product/smm';
$route['product/smm/add'] = 'Product/add_smm';
$route['product/smm/add/process'] = 'Product/process_add_smm';
$route['product/smm/custom/add'] = 'Product/add_custom_smm';
$route['product/smm/custom/remove'] = 'Product/process_remove_custom_smm';
$route['product/smm/datatables'] = 'Product/datatables_smm';
$route['product/smm/filter'] = 'Product/filter_smm';
$route['product/smm/edit/(:num)'] = 'Product/edit_smm/$1';
$route['product/smm/edit/(:num)/process'] = 'Product/process_edit_smm/$1';
$route['product/smm/bulk/delete'] = 'Product/bulk_delete_smm';
$route['product/smm/bulk/delete/datatables'] = 'Product/datatables_bulk_delete_smm';
$route['product/smm/bulk/delete/process'] = 'Product/process_bulk_delete_smm';
$route['product/smm/bulk/delete/all'] = 'Product/bulk_delete_all_smm';
$route['product/smm/bulk/add'] = 'Product/bulk_add_smm';
$route['product/smm/bulk/add/select/category'] = 'Product/select_category_smm';
$route['product/smm/bulk/add/data'] = 'Product/get_data_smm';
$route['product/smm/bulk/add/process'] = 'Product/process_bulk_add_smm';

$route['product/information'] = 'Product/information';
$route['product/information/datatables'] = 'Product/datatables_information';
$route['product/information/filter'] = 'Product/filter_information';

$route['report/profit/monthly'] = 'Report/Profit/monthly';
$route['report/profit/monthly/export'] = 'Report/Profit/process_export_excel';
$route['report/profit/monthly/datatables'] = 'Report/Profit/datatables_monthly';
$route['report/profit/chart_data_monthly'] = 'Report/Profit/chart_data_monthly';

$route['database/stockproduct'] = 'Database/Stockproduct';
$route['database/stockproduct/datatables'] = 'Database/Stockproduct/datatables';
$route['database/stockproduct/add'] = 'Database/Stockproduct/add';
$route['database/stockproduct/add/process'] = 'Database/Stockproduct/process_add';
$route['database/stockproduct/edit/(:num)'] = 'Database/Stockproduct/edit/$1';
$route['database/stockproduct/edit/(:num)/process'] = 'Database/Stockproduct/process_edit/$1';
$route['database/stockproduct/delete'] = 'Database/Stockproduct/process_delete';

$route['database/stockproduct/detail/(:num)'] = 'Database/Stockproduct/detail/$1';
$route['database/stockproduct/detail/(:num)/datatables'] = 'Database/Stockproduct/datatables_detail/$1';
$route['database/stockproduct/detail/(:num)/add'] = 'Database/Stockproduct/add_detail/$1';
$route['database/stockproduct/detail/(:num)/add/process'] = 'Database/Stockproduct/process_add_detail/$1';
$route['database/stockproduct/detail/(:num)/edit/(:num)'] = 'Database/Stockproduct/edit_detail/$1/$2';
$route['database/stockproduct/detail/(:num)/edit/(:num)/process'] = 'Database/Stockproduct/process_edit_detail/$1/$2';
$route['database/stockproduct/detail/(:num)/delete'] = 'Database/Stockproduct/process_delete_detail/$1';

$route['manage/addpages'] = 'Manage/AddPages';
$route['manage/addpages/datatables'] = 'Manage/AddPages/datatables_pages';
$route['manage/addpages/add'] = 'Manage/AddPages/add';
$route['manage/addpages/add/process'] = 'Manage/AddPages/process_add_pages';
$route['manage/addpages/edit/(:num)'] = 'Manage/AddPages/edit/$1';
$route['manage/addpages/edit/(:num)/process'] = 'Manage/AddPages/process_edit_pages/$1';
$route['manage/addpages/delete'] = 'Manage/AddPages/process_delete_pages';

$route['manage/category/product'] = 'Manage/Category/product';
$route['manage/category/product/delete'] = 'Manage/Category/process_delete_manual_product';
$route['manage/category/product/disable'] = 'Manage/Category/process_disable_category_product';
$route['manage/category/product/enable'] = 'Manage/Category/process_enable_category_product';
$route['manage/category/product/add'] = 'Manage/Category/add_category_product';
$route['manage/category/product/add/process'] = 'Manage/Category/process_add_category_product';
$route['manage/category/product/edit'] = 'Manage/Category/edit_category_product/$1';
$route['manage/category/product/edit/process'] = 'Manage/Category/process_edit_category_product/$1';
$route['manage/category/product/brand/(:any)'] = 'Manage/Category/brand_category_product/$1';
$route['manage/category/product/brand/(:any)/add'] = 'Manage/Brand/modal_add/$1';
$route['manage/category/product/brand/(:any)/add/process'] = 'Manage/Brand/process_add_brand/$1';
$route['manage/category/product/brand/(:any)/delete'] = 'Manage/Category/process_delete_brand_category_product/$1';
$route['manage/category/product/brand/(:any)/disable'] = 'Manage/Category/process_disable_brand_category_product/$1';
$route['manage/category/product/brand/(:any)/enable'] = 'Manage/Category/process_enable_brand_category_product/$1';
$route['manage/category/product/brand/(:any)/edit'] = 'Manage/Category/edit_brand_category_product/$1';
$route['manage/category/product/brand/(:any)/edit/process'] = 'Manage/Category/process_edit_brand_category_product/$1';
$route['manage/category/product/brand/(:any)/icon'] = 'Manage/Category/icon_brand_category_product/$1';
$route['manage/category/product/brand/(:any)/icon/process'] = 'Manage/Category/process_icon_brand_category_product/$1';
$route['manage/category/product/restore'] = 'Manage/Category/restore_category_product';

$route['manage/brand/add'] = 'Manage/Brand/modal_add';
$route['manage/brand/add/process'] = 'Manage/Brand/process_add_brand';
$route['manage/brand/select'] = 'Manage/Brand/select_brand';

$route['manage/news'] = 'Manage/News';
$route['manage/news/datatables'] = 'Manage/News/datatables_news';
$route['manage/news/add'] = 'Manage/News/add';
$route['manage/news/add/process'] = 'Manage/News/process_add_news';
$route['manage/news/edit/(:num)'] = 'Manage/News/edit/$1';
$route['manage/news/edit/(:num)/process'] = 'Manage/News/process_edit_news/$1';
$route['manage/news/delete'] = 'Manage/News/process_delete_news';

$route['manage/onvayaacademy'] = 'Manage/OnvayaAcademy';
$route['manage/onvayaacademy/datatables'] = 'Manage/OnvayaAcademy/datatables_academy';
$route['manage/onvayaacademy/add'] = 'Manage/OnvayaAcademy/add';
$route['manage/onvayaacademy/add/process'] = 'Manage/OnvayaAcademy/process_add_academy';
$route['manage/onvayaacademy/edit/(:num)'] = 'Manage/OnvayaAcademy/edit/$1';
$route['manage/onvayaacademy/edit/(:num)/process'] = 'Manage/OnvayaAcademy/process_edit_academy/$1';
$route['manage/onvayaacademy/delete'] = 'Manage/OnvayaAcademy/process_delete_academy';

$route['manage/broadcastemailmarketing'] = 'Manage/BroadcastEmailMarketing';
$route['manage/broadcastemailmarketing/datatables'] = 'Manage/BroadcastEmailMarketing/datatables_broadcast';
$route['manage/broadcastemailmarketing/add'] = 'Manage/BroadcastEmailMarketing/add';
$route['manage/broadcastemailmarketing/add/process'] = 'Manage/BroadcastEmailMarketing/process_add_broadcast';
$route['manage/broadcastemailmarketing/view/(:num)'] = 'Manage/BroadcastEmailMarketing/view/$1';
$route['manage/broadcastemailmarketing/edit/(:num)'] = 'Manage/BroadcastEmailMarketing/edit/$1';
$route['manage/broadcastemailmarketing/edit/(:num)/process'] = 'Manage/BroadcastEmailMarketing/process_edit_broadcast/$1';
$route['manage/broadcastemailmarketing/delete'] = 'Manage/BroadcastEmailMarketing/process_delete_broadcast';

$route['academy'] = 'Academy';
$route['academy/detail/(:num)'] = 'Academy/detail/$1';
$route['academy/embed/(:num)'] = 'Academy/get_embed_url/$1';

$route['manage/faq'] = 'Manage/Faq';
$route['manage/faq/datatables'] = 'Manage/Faq/datatables_faq';
$route['manage/faq/add'] = 'Manage/Faq/add';
$route['manage/faq/add/process'] = 'Manage/Faq/process_add_faq';
$route['manage/faq/edit/(:num)'] = 'Manage/Faq/edit/$1';
$route['manage/faq/edit/(:num)/process'] = 'Manage/Faq/process_edit_faq/$1';
$route['manage/faq/delete'] = 'Manage/Faq/process_delete_faq';

$route['manage/customersupport'] = 'Manage/CustomerSupport';
$route['manage/customersupport/datatables'] = 'Manage/CustomerSupport/datatables_customersupport';
$route['manage/customersupport/add'] = 'Manage/CustomerSupport/add';
$route['manage/customersupport/add/process'] = 'Manage/CustomerSupport/process_add_customersupport';
$route['manage/customersupport/edit/(:num)'] = 'Manage/CustomerSupport/edit/$1';
$route['manage/customersupport/edit/(:num)/process'] = 'Manage/CustomerSupport/process_edit_customersupport/$1';
$route['manage/customersupport/delete'] = 'Manage/CustomerSupport/process_delete_customersupport';

$route['manage/prefixoperator'] = 'Manage/PrefixOperator';
$route['manage/prefixoperator/datatables'] = 'Manage/PrefixOperator/datatables_prefixoperator';
$route['manage/prefixoperator/detail'] = 'Manage/PrefixOperator/detail_prefixoperator';
$route['manage/prefixoperator/detail/process'] = 'Manage/PrefixOperator/process_detail_prefixoperator';

$route['manage/member'] = 'Manage/Member';
$route['manage/member/datatables'] = 'Manage/Member/datatables_member';
$route['manage/member/add'] = 'Manage/Member/add';
$route['manage/member/add/process'] = 'Manage/Member/process_add_member';
$route['manage/member/edit/(:num)'] = 'Manage/Member/edit/$1';
$route['manage/member/edit/(:num)/process'] = 'Manage/Member/process_edit_member/$1';
$route['manage/member/extend/(:any)'] = 'Manage/Member/extend/$1';
$route['manage/member/extend/(:any)/process'] = 'Manage/Member/process_extend/$1';
$route['manage/member/members'] = 'Manage/Member/members';
$route['manage/member/members/datatables'] = 'Manage/Member/datatables_members';
$route['manage/member/export/(:any)'] = 'Manage/Member/process_export_excel/$1';

$route['manage/vendor'] = 'Manage/Vendor';
$route['manage/vendor/datatables'] = 'Manage/Vendor/datatables_vendor';
$route['manage/vendor/add'] = 'Manage/Vendor/add';
$route['manage/vendor/add/process'] = 'Manage/Vendor/process_add_vendor';
$route['manage/vendor/edit/(:num)'] = 'Manage/Vendor/edit/$1';
$route['manage/vendor/edit/(:num)/process'] = 'Manage/Vendor/process_edit_vendor/$1';
$route['manage/vendor/delete'] = 'Manage/Vendor/process_delete_vendor';
$route['manage/vendor/detail/(:num)'] = 'Manage/Vendor/detail/$1';
$route['manage/vendor/detail/(:num)/datatables'] = 'Manage/Vendor/datatables_detail_vendor/$1';
$route['manage/vendor/detail/(:num)/add'] = 'Manage/Vendor/add_detail/$1';
$route['manage/vendor/detail/(:num)/add/process'] = 'Manage/Vendor/process_add_detail_vendor/$1';
$route['manage/vendor/detail/(:num)/edit'] = 'Manage/Vendor/edit_detail/$1';
$route['manage/vendor/detail/(:num)/edit/process'] = 'Manage/Vendor/process_edit_detail_vendor/$1/$2';
$route['manage/vendor/detail/(:num)/delete'] = 'Manage/Vendor/process_delete_detail_vendor/$1';

$route['manage/privacypolicy'] = 'Manage/PrivacyPolicy';
$route['manage/privacypolicy/process'] = 'Manage/PrivacyPolicy/process_edit_privacypolicy';

$route['manage/termsofservice'] = 'Manage/TermsOfService';
$route['manage/termsofservice/process'] = 'Manage/TermsOfService/process';

$route['manage/livechat'] = 'Manage/Livechat';
$route['manage/livechat/process'] = 'Manage/Livechat/process_edit_livechat';

$route['manage/customapp'] = 'Manage/CustomApplication';
$route['manage/customapp/process'] = 'Manage/CustomApplication/process_customapplication';
$route['manage/customapp/request'] = 'Manage/CustomApplication/request';
$route['manage/customapp/request/build'] = 'Manage/CustomApplication/build';
$route['manage/customapp/requestuploadplaystore/process'] = 'Manage/CustomApplication/process_requestuploadplaystore';
$route['manage/customapp/reason'] = 'Manage/CustomApplication/modal_reason';
$route['manage/customapp/prerelease'] = 'Manage/CustomApplication/prerelease';
$route['manage/customapp/prerelease/datatables'] = 'Manage/CustomApplication/datatables_prerelease';
$route['manage/customapp/prerelease/process'] = 'Manage/CustomApplication/process_prerelease';
$route['manage/customapp/prerelease/delete'] = 'Manage/CustomApplication/process_delete_prerelease';

$route['manage/slider'] = 'Manage/Slider';
$route['manage/slider/add'] = 'Manage/Slider/add';
$route['manage/slider/add/process'] = 'Manage/Slider/process_add_slider';
$route['manage/slider/datatables'] = 'Manage/Slider/datatables_slider';
$route['manage/slider/delete'] = 'Manage/Slider/process_delete_slider';

$route['manage/invoices'] = 'Manage/Invoice';
$route['manage/invoices/datatables'] = 'Manage/Invoice/datatables_invoices';
$route['manage/invoices/pay'] = 'Manage/Invoice/pay';

$route['manage/icons'] = 'Manage/Icons';
$route['manage/icons/add'] = 'Manage/Icons/add';
$route['manage/icons/add/process'] = 'Manage/Icons/process_add_icons';
$route['manage/icons/datatables'] = 'Manage/Icons/datatables_icons';
$route['manage/icons/edit/(:num)'] = 'Manage/Icons/edit/$1';
$route['manage/icons/edit/(:num)/process'] = 'Manage/Icons/process_edit_icons/$1';
$route['manage/icons/delete'] = 'Manage/Icons/process_delete_icons';

$route['manage/role'] = 'Manage/Role';
$route['manage/role/datatables'] = 'Manage/Role/datatables_role';
$route['manage/role/add'] = 'Manage/Role/add';
$route['manage/role/add/process'] = 'Manage/Role/process_add_role';
$route['manage/role/edit'] = 'Manage/Role/edit';
$route['manage/role/edit/process'] = 'Manage/Role/process_edit_role';
$route['manage/role/delete'] = 'Manage/Role/process_delete_role';
$route['manage/role/delete/discountadv'] = 'Manage/Role/process_delete_role_discountadv';
$route['manage/role/modaladvanced'] = 'Manage/Role/modalAdvanced';

$route['manage/platformsosmed'] = 'Manage/PlatformSosmed';
$route['manage/platformsosmed/add'] = 'Manage/PlatformSosmed/add';
$route['manage/platformsosmed/add/process'] = 'Manage/PlatformSosmed/process_add_platformsosmed';
$route['manage/platformsosmed/datatables'] = 'Manage/PlatformSosmed/datatables_platformsosmed';
$route['manage/platformsosmed/edit/(:num)'] = 'Manage/PlatformSosmed/edit/$1';
$route['manage/platformsosmed/edit/(:num)/process'] = 'Manage/PlatformSosmed/process_edit_platformsosmed/$1';
$route['manage/platformsosmed/delete'] = 'Manage/PlatformSosmed/process_delete_platformsosmed';
$route['manage/platformsosmed/detail/(:num)'] = 'Manage/PlatformSosmed/detail/$1';
$route['manage/platformsosmed/detail/(:num)/process'] = 'Manage/PlatformSosmed/process_detail_platformsosmed/$1';

$route['manage/historyrequestuploadplaystore'] = 'Manage/HistoryRequestUploadPlaystore';
$route['manage/historyrequestuploadplaystore/datatables'] = 'Manage/HistoryRequestUploadPlaystore/datatables_history_uploadplaystore';
$route['manage/historyrequestuploadplaystore/confirm'] = 'Manage/HistoryRequestUploadPlaystore/process_confirm_uploadplaystore';
$route['manage/historyrequestuploadplaystore/cancel/(:any)'] = 'Manage/HistoryRequestUploadPlaystore/cancel_uploadplaystore/$1';
$route['manage/historyrequestuploadplaystore/cancel/(:any)/process'] = 'Manage/HistoryRequestUploadPlaystore/process_cancel_uploadplaystore/$1';
$route['manage/historyrequestuploadplaystore/reason'] = 'Manage/HistoryRequestUploadPlaystore/modal_reason';

$route['settings/account'] = 'Settings/account';
$route['settings/account/process/personal'] = 'Settings/process_change_personal_account';
$route['settings/account/process/company'] = 'Settings/process_change_company_account';
$route['settings/account/process/uniquenominal'] = 'Settings/process_unique_nominal';

$route['settings/password'] = 'Settings/password';
$route['settings/password/process'] = 'Settings/process_change_password';

$route['settings/seo'] = 'Settings/seo';
$route['settings/seo/process'] = 'Settings/process_seo';

$route['settings/apikey'] = 'Settings/apikey';
$route['settings/apikey/process/ppob'] = 'Settings/process_setting_ppob';
$route['settings/apikey/process/smm'] = 'Settings/process_setting_smm';
$route['settings/apikey/set/multivendor'] = 'Settings/set_multivendor';
$route['settings/apikey/set/singlevendor'] = 'Settings/set_singlevendor';
$route['settings/apikey/datatables'] = 'Manage/Vendor/datatables_vendor';
$route['settings/apikey/add'] = 'Manage/Vendor/add';
$route['settings/apikey/add/process'] = 'Manage/Vendor/process_add_vendor';
$route['settings/apikey/edit/(:num)'] = 'Manage/Vendor/edit/$1';
$route['settings/apikey/edit/(:num)/process'] = 'Manage/Vendor/process_edit_vendor/$1';
$route['settings/apikey/delete'] = 'Manage/Vendor/process_delete_vendor';
$route['settings/apikey/delete_product'] = 'Manage/Vendor/process_delete_product';
$route['settings/apikey/detail/(:num)'] = 'Manage/Vendor/detail/$1';
$route['settings/apikey/detail/(:num)/datatables'] = 'Manage/Vendor/datatables_detail_vendor/$1';
$route['settings/apikey/detail/(:num)/add'] = 'Manage/Vendor/add_detail/$1';
$route['settings/apikey/detail/(:num)/add/process'] = 'Manage/Vendor/process_add_detail_vendor/$1';
$route['settings/apikey/detail/(:num)/edit'] = 'Manage/Vendor/edit_detail/$1';
$route['settings/apikey/detail/(:num)/edit/process'] = 'Manage/Vendor/process_edit_detail_vendor/$1/$2';
$route['settings/apikey/detail/(:num)/delete'] = 'Manage/Vendor/process_delete_detail_vendor/$1';
$route['settings/apikey/configuration'] = 'Settings/configuration_apikey';
$route['settings/apikey/configuration/process'] = 'Settings/process_configuration_apikey';
$route['settings/apikey/status'] = 'Settings/process_status_apikey';

$route['settings/margins'] = 'Settings/margin';
$route['settings/margins/datatables'] = 'Settings/datatables_margin';
$route['settings/margins/add'] = 'Settings/add_margin';
$route['settings/margins/add/process'] = 'Settings/process_add_margin';
$route['settings/margins/edit'] = 'Settings/edit_margin';
$route['settings/margins/edit/process'] = 'Settings/process_edit_margin';
$route['settings/margins/delete'] = 'Settings/process_delete_margin';

$route['settings/domain'] = 'Settings/domain';
$route['settings/domain/process'] = 'Settings/process_domain';
$route['settings/domain/request'] = 'Settings/request_domain';

$route['settings/theme'] = 'Settings/theme';
$route['settings/theme/change'] = 'Settings/process_change_theme';
$route['settings/theme/configuration'] = 'Settings/configuration_theme';
$route['settings/theme/configuration/process'] = 'Settings/process_configuration_theme';

$route['settings/smtp'] = 'Settings/smtp';
$route['settings/smtp/process'] = 'Settings/process_smtp';

$route['settings/logmessage'] = 'Settings/logmessage';
$route['settings/logmessage/datatables'] = 'Settings/datatables_logmessage';

$route['settings/struk'] = 'Settings/struk';
$route['settings/struk/process'] = 'Settings/process_struk';

$route['settings/whatsapp'] = 'Settings/whatsapp';
$route['settings/whatsapp/process'] = 'Settings/process_whatsapp';
$route['settings/whatsapp/process/deposit'] = 'Settings/process_whatsapp_deposit';
$route['settings/whatsapp/configuration'] = 'Settings/configuration_whatsapp';

$route['settings/firebase'] = 'Settings/firebase';
$route['settings/firebase/process/transaction'] = 'Settings/process_firebase_transaction';
$route['settings/firebase/process/deposit'] = 'Settings/process_firebase_deposit';

$route['addons'] = 'Addons';
$route['addons/buy/application'] = 'Addons/buy_application';
$route['addons/buy/application/process'] = 'Addons/process_buy_application';
$route['addons/buy/emailmarketing'] = 'Addons/buy_emailmarketing';
$route['addons/process_buy_emailmarketing'] = 'Addons/process_buy_emailmarketing';

$route['upgrade'] = 'Upgrade';

$route['select/category'] = 'Select/category';
$route['select/brand'] = 'Select/brand';
$route['select/product'] = 'Select/product';
$route['select/productsmm'] = 'Select/product_smm';
$route['select/database'] = 'Select/database';

$route['cronjobs/build/application'] = 'Cronjobs/build_application';

$route['cronjobs/topup/transaction'] = 'Cronjobs/topup_transaction';

$route['cronjobs/member/role/default'] = 'Cronjobs/member_role_default';

$route['cronjobs/member/topup/transaction/expired'] = 'Cronjobs/member_topup_transaction_expired';
$route['cronjobs/member/topup/transaction/bca'] = 'Cronjobs/member_topup_transaction_bca';
$route['cronjobs/member/topup/transaction/gopay'] = 'Cronjobs/member_topup_transaction_gopay';
$route['cronjobs/member/topup/transaction/ovo'] = 'Cronjobs/member_topup_transaction_ovo';
$route['cronjobs/member/topup/transaction/paymentgateway'] = 'Cronjobs/member_topup_transaction_paymentgateway';

$route['cronjobs/member/product/transaction'] = 'Cronjobs/member_product_transaction';
$route['cronjobs/member/product/transaction/paymentgateway'] = 'Cronjobs/member_product_transaction_paymentgateway';
$route['cronjobs/member/product/transaction/queue'] = 'Cronjobs/member_product_transaction_queue';
$route['cronjobs/member/product/transaction/queue/expired'] = 'Cronjobs/member_product_transaction_queue_expired';
$route['cronjobs/member/product/transaction/queue/processing'] = 'Cronjobs/member_product_transaction_queue_processing';

$route['cronjobs/guest/product/transaction/queue/expired'] = 'Cronjobs/guest_product_transaction_queue_expired';
$route['cronjobs/guest/product/transaction/bca'] = 'Cronjobs/member_product_transaction_bca';

$route['cronjobs/member/history/login'] = 'Cronjobs/member_history_login';

$route['cronjobs/member/invoice'] = 'Cronjobs/member_invoice';
$route['cronjobs/member/invoice/expired'] = 'Cronjobs/member_invoice_expired';

$route['cronjobs/member/domain/request'] = 'Cronjobs/member_domain_request';

$route['cronjobs/product/data/ppob'] = 'Cronjobs/product_data';
$route['cronjobs/product/data/smm'] = 'Cronjobs/product_data_smm';

$route['cronjobs/product/information'] = 'Cronjobs/product_information';
$route['cronjobs/product/revalidate'] = 'Cronjobs/product_revalidate';
$route['cronjobs/product/delete'] = 'Cronjobs/product_delete';

$route['cronjobs/account/errorlog'] = 'Cronjobs/account_errorlog';

$route['cronjobs/broadcast/email/marketing'] = 'Cronjobs/broadcast_email_marketing';

$route['cronjobs/merchant/balance'] = 'Cronjobs/merchant_balance';

$route['cronjobs/multivendor/profile'] = 'Cronjobs/multivendor_profile';
$route['cronjobs/multivendor/service'] = 'Cronjobs/multivendor_service';
$route['cronjobs/multivendor/status'] = 'Cronjobs/multivendor_status';
$route['cronjobs/multivendor/transaction/queue'] = 'Cronjobs/multivendor_transaction_queue';
$route['cronjobs/multivendor/transaction/queue/processing'] = 'Cronjobs/multivendor_transaction_queue_processing';

$route['api/socket/user'] = 'API/APISocket/user';
$route['api/mobile/auth/login'] = 'API/Mobile/auth_login';
$route['api/mobile/auth/logout'] = 'API/Mobile/auth_logout';
$route['api/mobile/notification/store'] = 'API/Mobile/notification_store';

$route['api/applications/upload'] = 'API/Applications/upload';
