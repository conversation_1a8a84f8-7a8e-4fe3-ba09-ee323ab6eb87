<?php
defined('BASEPATH') or die('No direct script access allowed!');

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

/**
 * @property Datatables $datatables
 * @property MsUsers $msusers
 * @property MsRole $msrole
 * @property CI_DB_mysqli_driver $db
 * @property Invoices $invoices
 * @property MsLicense $mslicense
 * @property TrOrder $trorder
 * @property ThemeConfiguration $themeconfiguration
 */
class Member extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('Invoices', 'invoices');
        $this->load->model('MsLicense', 'mslicense');
        $this->load->model('TrOrder', 'trorder');
        $this->load->model('ThemeConfiguration', 'themeconfiguration');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Member';
        $data['content'] = 'manage/member/index';

        return $this->load->view('master', $data);
    }

    public function datatables_member()
    {
        try {
            if (isLogin() && isAdmin()) {
                $data = array();

                $datatables = $this->datatables->make('MsUsers', 'QueryDatatables_member', 'SearchDatatables');

                $where = array(
                    'a.merchantid' => null
                );

                foreach ($datatables->getData($where) as $key => $value) {
                    if ($value->licenseid != null) {
                        $expireddate = date('d F Y', strtotime($value->expireddate));

                        if ($value->expireddate < getCurrentDate()) {
                            $statusaccount = "<span class=\"badge badge-light-danger\">Premium - $expireddate (Expired)</span>";
                        } else {
                            $statusaccount = "<span class=\"badge badge-light-success\">Premium - $expireddate</span>";
                        }
                    } else {
                        $statusaccount = "<span class=\"badge badge-light-danger\">Free</span>";
                    }

                    $detail = array();
                    $detail[] = $value->name;
                    $detail[] = $value->email;
                    $detail[] = $value->companycategory;
                    $detail[] = IDR($value->total ?? 0);
                    $detail[] = "<a href=\"javascript:;\" onclick=\"members('" . stringEncryption('encrypt', $value->id) . "')\">" . IDR($value->totalusers ?? 0) . "</a>";
                    $detail[] = $value->companyname;
                    $detail[] = $value->companyaddress;
                    $detail[] = $statusaccount;

                    if ($value->domain != null) {
                        $action = "<a href=\"https://" . $value->domain . "\" class=\"btn btn-icon btn-warning btn-sm mb-1 me-1\" target=\"_blank\">
                            <i class=\"fa fa-external-link\"></i>
                        </a>";

                        if ($value->phonenumber != null) {
                            $action .= "<a href=\"https://api.whatsapp.com/send?phone=" . changePrefixPhone($value->phonenumber) . "\" class=\"btn btn-icon btn-success btn-sm mb-1 me-1\" target=\"_blank\">
                                <i class=\"fab fa-whatsapp\"></i>
                            </a>";
                        }
                    } else {
                        if ($value->phonenumber != null) {
                            $action = "<a href=\"https://api.whatsapp.com/send?phone=" . changePrefixPhone($value->phonenumber) . "\" class=\"btn btn-icon btn-success btn-sm mb-1 me-1\" target=\"_blank\">
                                <i class=\"fab fa-whatsapp\"></i>
                            </a>";
                        } else {
                            $action = "";
                        }
                    }

                    $action .= " <a href=\"javascript:;\" class=\"btn btn-icon btn-primary btn-sm mb-1 me-1\" onclick=\"modalExtend('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa-solid fa-forward\"></i>
                    </a>";

                    $action .= "<a href=\"" . base_url('manage/member/edit/' . $value->id) . "\" class=\"btn btn-icon btn-warning btn-sm mb-1 me-1\">
                        <i class=\"fa fa-edit\"></i>
                    </a>";

                    $detail[] = $action;

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tambah Member';
        $data['content'] = 'manage/member/add';

        return $this->load->view('master', $data);
    }

    public function process_add_member()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isAdmin()) {
                throw new Exception('Access denied');
            }

            $name = getPost('name');
            $email = getPost('email');
            $password = getPost('password');
            $googletag = getPost('googletag');

            if ($name == null) {
                throw new Exception('Nama wajib diisi');
            } else if (preg_match('/[^a-zA-Z\s]+/', $name)) {
                throw new Exception('Nama harus berupa huruf saja');
            } else if ($email == null) {
                throw new Exception('Alamat email wajib diisi');
            } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Alamat email yang anda masukkan tidak valid');
            } else if ($password == null) {
                throw new Exception('Kata sandi wajib diisi');
            } else if (!password_strength_check($password)) {
                throw new Exception('Kata sandi minimal memiliki 8 karakter atau lebih dengan campuran huruf, angka & simbol');
            } else {
                $name = removeSymbol($name);
            }

            $get = $this->msusers->total(array(
                'email' => $email,
                'merchantid' => null
            ));

            if ($get > 0) {
                throw new Exception('Alamat email yang anda masukkan telah terdaftar');
            }

            $insert = array();
            $insert['name'] = $name;
            $insert['email'] = $email;
            $insert['isemailverified'] = 1;
            $insert['password'] = password_hash($password, PASSWORD_DEFAULT);
            $insert['role'] = 'User';
            $insert['googletag'] = $googletag;

            $this->msusers->insert($insert);
            $userid = $this->db->insert_id();

            $insertrole = array();
            $insertrole['rolename'] = 'User';
            $insertrole['isdefault'] = 1;
            $insertrole['createddate'] = getCurrentDate();
            $insertrole['createdby'] = $userid;
            $insertrole['discounttype'] = 'Simple';

            $this->msrole->insert($insertrole);

            // Assign default "Able" theme to new user
            $this->load->model('ThemeConfiguration', 'themeconfiguration');
            $inserttheme = array();
            $inserttheme['userid'] = $userid;
            $inserttheme['themename'] = 'Able';
            $inserttheme['isused'] = 1;
            $inserttheme['themeconfig'] = json_encode(array(
                'company' => 'Server PPOB & SMM',
            ));
            $this->themeconfiguration->insert($inserttheme);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan member');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menambahkan member');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        if ($id == null) {
            return redirect(base_url('manage/member'));
        }

        $cek = $this->msusers->get(array(
            'id' => $id,
            'merchantid' => null,
        ));

        if ($cek->num_rows() == 0) {
            return redirect(base_url('manage/member'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Ubah Member';
        $data['content'] = 'manage/member/edit';
        $data['member'] = $cek->row();

        return $this->load->view('master', $data);
    }

    public function process_edit_member($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isAdmin()) {
                throw new Exception('Access denied');
            }

            $name = getPost('name');
            $password = getPost('password');
            $googletag = getPost('googletag');

            if ($name == null) {
                throw new Exception('Nama wajib diisi');
            } else if (preg_match('/[^a-zA-Z\s]+/', $name)) {
                throw new Exception('Nama harus berupa huruf saja');
            } else if ($password != null) {
                if (!password_strength_check($password)) {
                    throw new Exception('Kata sandi minimal memiliki 8 karakter atau lebih dengan campuran huruf, angka & simbol');
                }
            }

            if ($id == null) {
                throw new Exception('ID Member tidak boleh kosong');
            }

            $cek = $this->msusers->total(array(
                'id' => $id,
                'merchantid' => null,
            ));

            if ($cek == 0) {
                throw new Exception('Member tidak ditemukan');
            }

            $update = array();
            $update['name'] = $name;
            $update['googletag'] = $googletag;

            if ($password != null) {
                $update['password'] = password_hash($password, PASSWORD_DEFAULT);
            }

            $this->msusers->update(array(
                'id' => $id,
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data member');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengubah data member');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function extend($id)
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isAdmin()) {
                throw new Exception('Access denied');
            }

            $id = stringEncryption('decrypt', $id);

            $cekuser = $this->msusers->total(array(
                'id' => $id,
                'merchantid' => null,
            ));

            if ($cekuser == 0) {
                throw new Exception('Member tidak ditemukan');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/member/modalextend', array(
                    'license' => $this->mslicense->order_by('price', 'ASC')->result()
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_extend($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isAdmin()) {
                throw new Exception('Access denied');
            }

            $id = stringEncryption('decrypt', $id);

            $cekuser = $this->msusers->get(array(
                'id' => $id,
            ));

            if ($cekuser->num_rows() == 0) {
                throw new Exception('Member tidak ditemukan');
            }

            $user_row = $cekuser->row();

            $month = getPost('month');
            $licenseid = getPost('licenseid');

            if ($month == null) {
                throw new Exception('Durasi tidak boleh kosong');
            } else if ($month < 1) {
                throw new Exception('Durasi tidak boleh kurang dari 1 bulan');
            } else if ($month > 12) {
                throw new Exception('Durasi tidak boleh lebih dari 12 bulan');
            } else if ($licenseid == null) {
                throw new Exception('Lisensi tidak boleh kosong');
            } else {
                $get_license = $this->mslicense->total(array(
                    'id' => $licenseid,
                ));

                if ($get_license == 0) {
                    throw new Exception('Lisensi tidak ditemukan');
                }
            }

            $row = $cekuser->row();

            $update = array();

            if ($row->expireddate < getCurrentDate() || $row->expireddate == null) {
                $update = array(
                    'expireddate' => date('Y-m-d', strtotime('+' . $month . ' month')),
                );
            } else {
                $update = array(
                    'expireddate' => date('Y-m-d', strtotime('+' . $month . ' month', strtotime($row->expireddate))),
                );
            }

            if ($row->licenseid == null) {
                $update['licenseid'] = $licenseid;
            }

            $this->msusers->update(array(
                'id' => $id,
            ), $update);

            $cekinvoices = $this->invoices->total(array(
                'targetuserid' => $id,
                'status' => 'Pending',
                'invoicetype' => 'Extend License'
            ));

            if ($cekinvoices > 0) {
                $updateinvoice = array();
                $updateinvoice['status'] = 'Paid';

                $this->invoices->update(array(
                    'targetuserid' => $id,
                    'status' => 'Pending',
                    'invoicetype' => 'Extend License'
                ), $updateinvoice);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal memperpanjang durasi member');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil memperpanjang durasi member');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function members()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Access denied');
            } else if (!isAdmin()) {
                throw new Exception('Access denied');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Member tidak boleh kosong');
            }

            $id = stringEncryption('decrypt', $id);

            $cek = $this->msusers->get(array(
                'id' => $id,
                'merchantid' => null,
            ));

            if ($cek->num_rows() == 0) {
                throw new Exception('Member tidak ditemukan');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('manage/member/modalusers', array(
                    'member' => $cek->row(),
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_members()
    {
        try {
            if (isLogin() && isAdmin()) {
                $merchantid = getPost('merchantid');

                if ($merchantid == null) {
                    throw new Exception('Merchant tidak boleh kosong');
                }

                $get = $this->msusers->total(array(
                    'id' => $merchantid,
                    'merchantid' => null,
                ));

                if ($get == 0) {
                    throw new Exception('Merchant tidak ditemukan');
                }

                $data = array();

                $datatables = $this->datatables->make('MsUsers', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.merchantid' => $merchantid,
                    'a.isdeleted' => null,
                );

                foreach ($datatables->getData($where) as $key => $value) {
                    $lastOrder = $this->trorder->order_by('createddate', 'DESC')->get(array(
                        'a.merchantid_order' => getCurrentIdUser(),
                        'a.userid' => $value->id,
                    ))->row();

                    $detail = array();

                    if ($value->isemailverified == 1) {
                        $isemailverified = "<span class=\"badge badge-success\">Terverifikasi</span>";
                    } else {
                        $isemailverified = "<span class=\"badge badge-danger\">Belum Diverifikasi</span>";
                    }

                    $detail[] = $value->name;
                    $detail[] = $value->email;
                    $detail[] = $value->phonenumber ?? '-';
                    $detail[] = $lastOrder != null ? date('d F Y H:i:s', strtotime($lastOrder->createddate)) : '-';
                    $detail[] = IDR($value->transactiontotal);
                    $detail[] = IDR($value->transactiontotalppob);
                    $detail[] = IDR($value->transactiontotalsmm);
                    $detail[] = IDR($value->balance);
                    $detail[] = $isemailverified;

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_export_excel($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        try {
            $id = stringEncryption('decrypt', $id);

            $cek = $this->msusers->get(array(
                'id' => $id,
                'merchantid' => null,
            ));

            if ($cek->num_rows() == 0) {
                return redirect(base_url('manage/member'));
            }

            $member = $cek->row();

            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // Set title
            $sheet->setCellValue('A1', "Laporan Data Member - " . $member->name);
            $sheet->mergeCells('A1:I1');
            $sheet->getStyle('A1')->applyFromArray([
                'font' => ['bold' => true, 'size' => 14],
                'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER],
            ]);

            // Set header values
            $sheet->setCellValue('A2', "No")
                ->setCellValue('B2', "Nama")
                ->setCellValue('C2', "Email")
                ->setCellValue('D2', "Nomor Handphone")
                ->setCellValue('E2', "Tanggal Transaksi Terakhir")
                ->setCellValue('F2', "Total Transaksi")
                ->setCellValue('G2', "Transaksi PPOB")
                ->setCellValue('H2', "Transaksi SMM")
                ->setCellValue('I2', "Jumlah Saldo")
                ->setCellValue('J2', "Status Email");

            // Apply styles to header
            $headerStyleArray = [
                'font' => ['bold' => true],
                'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER],
            ];
            $sheet->getStyle('A2:J2')->applyFromArray($headerStyleArray);

            $getdata = $this->msusers->select('a.*')
                ->where(array(
                    'a.merchantid' => $id,
                    'a.isdeleted' => null,
                ))
                ->get()
                ->result();

            $rowstart = 3;

            foreach ($getdata as $key => $value) {
                // Get last transaction date
                $lastOrder = $this->trorder->order_by('createddate', 'DESC')->get(array(
                    'a.merchantid_order' => $id,
                    'a.userid' => $value->id,
                ))->row();

                $lastTransactionDate = $lastOrder ? date('d F Y H:i', strtotime($lastOrder->createddate)) : '-';

                // Get total transactions
                $totalTransactions = $this->trorder->total(array(
                    'a.merchantid_order' => $id,
                    'a.userid' => $value->id,
                ));

                // Get PPOB transactions
                $ppobTransactions = $this->trorder->total(array(
                    'a.merchantid_order' => $id,
                    'a.userid' => $value->id,
                    'a.type' => 'PPOB',
                ));

                // Get SMM transactions
                $smmTransactions = $this->trorder->total(array(
                    'a.merchantid_order' => $id,
                    'a.userid' => $value->id,
                    'a.type' => 'SMM',
                ));

                $sheet->setCellValue('A' . $rowstart, $key + 1)
                    ->setCellValue('B' . $rowstart, $value->name)
                    ->setCellValue('C' . $rowstart, $value->email)
                    ->setCellValueExplicit('D' . $rowstart, $value->phonenumber ?? '-', \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING)
                    ->setCellValue('E' . $rowstart, $lastTransactionDate)
                    ->setCellValue('F' . $rowstart, $totalTransactions)
                    ->setCellValue('G' . $rowstart, $ppobTransactions)
                    ->setCellValue('H' . $rowstart, $smmTransactions)
                    ->setCellValue('I' . $rowstart, IDR($value->balance))
                    ->setCellValue('J' . $rowstart, $value->isemailverified == 1 ? 'Terverifikasi' : 'Belum Diverifikasi');

                // Align Jumlah Saldo to the left
                $sheet->getStyle('I' . $rowstart)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

                // Set color based on status email verification
                if ($value->isemailverified == 1) {
                    $sheet->getStyle('J' . $rowstart)->applyFromArray([
                        'fill' => [
                            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                            'startColor' => ['argb' => 'FF00FF00'], // Green
                        ],
                    ]);
                } else {
                    $sheet->getStyle('J' . $rowstart)->applyFromArray([
                        'fill' => [
                            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                            'startColor' => ['argb' => 'FFFF0000'], // Red
                        ],
                    ]);
                }

                $rowstart++;
            }

            // Auto size columns
            foreach (range('A', 'J') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }

            // Apply border styles
            $sheet->getStyle("A1:J" . ($rowstart - 1))->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
            ]);

            $writer = new Xlsx($spreadsheet);
            $filename = "Laporan Data Member - " . $member->name;

            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
            header('Cache-Control: max-age=0');

            $writer->save('php://output');
        } catch (Exception $ex) {
            return redirect(base_url('manage/member'));
        }
    }
}
