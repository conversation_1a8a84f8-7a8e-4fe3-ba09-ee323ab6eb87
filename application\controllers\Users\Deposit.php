<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsPaymentMethod $paymentmethod
 * @property MsPaymentGateway $paymentgateway
 * @property HistoryWrongNominal $historywrongnominal
 * @property HistoryBalance $historybalance
 * @property ApiKeys_WhatsApp
 */
class Deposit extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Deposits', 'deposits');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsPaymentMethod', 'paymentmethod');
        $this->load->model('HistoryWrongNominal', 'historywrongnominal');
        $this->load->model('FeePaymentGateway', 'feepaymentgateway');
        $this->load->model('MsPaymentGateway', 'paymentgateway');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
    }

    public function history()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Riwayat Topup';
        $data['content'] = 'user/deposit/history';

        return $this->load->view('master', $data);
    }

    public function get_statistics()
    {
        try {
            if (!isLogin() || !isUser() || getCurrentUser()->licenseid == null) {
                throw new Exception('Access denied');
            }

            $merchantid = getCurrentIdUser();
            $date = getPost('date');
            $code = getPost('code');
            $payment = getPost('payment');
            $status = getPost('status');

            // Base where condition
            $where = array('b.merchantid' => $merchantid);

            // Apply filters
            if ($date != null) {
                $date = explode('-', $date);
                $date1 = isset($date[0]) ? trim($date[0]) : null;
                $date2 = isset($date[1]) ? trim($date[1]) : null;

                if ($date1 && $date2) {
                    $where['DATE(a.createddate) >='] = date('Y-m-d', strtotime($date1));
                    $where['DATE(a.createddate) <='] = date('Y-m-d', strtotime($date2));
                }
            }

            if ($code != null) {
                $where['a.code'] = $code;
            }

            if ($payment != null) {
                $where['a.paymenttype'] = $payment;
            }

            // Get successful deposits (nominal sum)
            $this->db->select('COALESCE(SUM(a.nominal), 0) as total');
            $this->db->from('deposit a');
            $this->db->join('msusers b', 'b.id = a.userid');
            $this->db->where($where);
            if ($status == null || $status == 'Success') {
                $this->db->where("(a.status = 'Success' OR a.status = 'settlement')");
            } else if ($status != 'Success') {
                $this->db->where('1=0'); // No results if filtering by other status
            }
            $successful = $this->db->get()->row()->total;

            // Get pending deposits (nominal sum)
            $this->db->select('COALESCE(SUM(a.nominal), 0) as total');
            $this->db->from('deposit a');
            $this->db->join('msusers b', 'b.id = a.userid');
            $this->db->where($where);
            if ($status == null || $status == 'Pending') {
                $this->db->where('a.status', 'Pending');
            } else if ($status != 'Pending') {
                $this->db->where('1=0'); // No results if filtering by other status
            }
            $pending = $this->db->get()->row()->total;

            // Get failed deposits (nominal sum)
            $this->db->select('COALESCE(SUM(a.nominal), 0) as total');
            $this->db->from('deposit a');
            $this->db->join('msusers b', 'b.id = a.userid');
            $this->db->where($where);
            if ($status == null || $status == 'Cancel') {
                $this->db->where("(a.status = 'Cancel' OR a.status = 'Expired' OR a.status = 'Failed')");
            } else if ($status != 'Cancel') {
                $this->db->where('1=0'); // No results if filtering by other status
            }
            $failed = $this->db->get()->row()->total;

            // Get bonus amount (only from successful deposits)
            $this->db->select('COALESCE(SUM(a.nominalbonus), 0) as total');
            $this->db->from('deposit a');
            $this->db->join('msusers b', 'b.id = a.userid');
            $this->db->where($where);
            $this->db->where('a.isbonus', 1);
            if ($status == null || $status == 'Success') {
                $this->db->where("(a.status = 'Success' OR a.status = 'settlement')");
            } else if ($status != 'Success') {
                $this->db->where('1=0'); // No results if filtering by other status
            }
            $bonus = $this->db->get()->row()->total;

            return JSONResponse(array(
                'status' => true,
                'data' => array(
                    'successful' => (float)$successful,
                    'pending' => (float)$pending,
                    'failed' => (float)$failed,
                    'bonus' => (float)$bonus
                )
            ));
        } catch (Exception $ex) {
            return JSONResponse(array(
                'status' => false,
                'message' => $ex->getMessage()
            ));
        }
    }

    public function datatables_history()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatable = $this->datatables->make('Deposits', 'QueryDatatables', 'SearchDatatables');

                $date = getPost('date');
                $code = getPost('code');
                $payment = getPost('payment');
                $sts = getPost('status');

                if ($date != null) {
                    $date = explode('-', $date);
                    $date1 = isset($date[0]) ? $date[0] : null;
                    $date2 = isset($date[1]) ? $date[1] : null;
                }

                $where = array();
                $where['b.merchantid'] = getCurrentIdUser();

                if ($date != null) {
                    $where['DATE(a.createddate) >='] = date('Y-m-d', strtotime($date1));
                    $where['DATE(a.createddate) <='] = date('Y-m-d', strtotime($date2));
                } else {
                    $where['DATE(a.createddate) >='] = date('Y-m-01');
                    $where['DATE(a.createddate) <='] = date('Y-m-t');
                }

                if ($code != null) {
                    $where['a.code'] = $code;
                }

                if ($payment != null) {
                    $where['a.paymenttype'] = $payment;
                }

                if ($sts != null) {
                    if ($sts == 'Cancel') {
                        $where["(a.status = 'Cancel' OR a.status = 'Expired' OR a.status = 'Failed')="] = true;
                    } else if ($sts == 'Success') {
                        $where["(a.status = 'Success' OR a.status = 'settlement')="] = true;
                    } else {
                        $where['a.status'] = $sts;
                    }
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    $detail = array();

                    if ($value->status == 'Pending') {
                        $status = "<span class=\"badge badge-warning\">Menunggu</span>";
                    } else if ($value->status == 'Success' || $value->status == 'settlement') {
                        $status = "<span class=\"badge badge-success\">Berhasil</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">Gagal</span>";
                    }

                    $actions = "N/A";

                    if ($value->status == 'Pending') {
                        $actions = "<a href=\"javascript:;\" class=\"btn btn-success btn-sm mb-1\" onclick=\"confirmPayment('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-check\"></i>
                            <span>Konfirmasi</span>
                        </a>

                        <button type=\"button\" class=\"btn btn-warning btn-sm mb-1\" onclick=\"wrongNominal('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-credit-card\"></i>
                            <span>Salah Nominal Transfer</span>
                        </button>

                        <button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"cancelPayment('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-times\"></i>
                            <span>Cancel</span>
                        </button>";
                    }

                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = "<b>$value->code</b><br><b>" . (strtoupper($value->depositplatform ?? 'web')) . "</b> - $value->buyer_name";
                    $detail[] = "<b>$value->payment</b><br>$value->paymenttype";
                    $detail[] = IDR($value->nominal);
                    $detail[] = IDR($value->nominal + $value->nominalbonus - $value->uniqueadmin - $value->fee);
                    $detail[] = $status;
                    $detail[] = $actions;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        try {
            // Kirim Firebase notification untuk deposit
            sendFirebaseNotificationDeposit($orderid, $userid);

            $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
                'userid' => $userid,
            ));

            if ($apikeys_whatsapp->num_rows() == 0) {
                return false;
            }

            $row = $apikeys_whatsapp->row();

            $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

            $messagenotification = replaceParameterNotificationDeposit($orderid, $userid);

            if ($messagenotification != null && $phonenumber != null) {
                $phonenumber = changePrefixPhone($phonenumber);

                $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

                if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                    log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
                }

                return true;
            }

            return false;
        } catch (Exception $ex) {
            return false;
        }
    }

    public function process_confirm_topup()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'a.merchantid' => getCurrentIdUser(),
                    'a.status' => 'Pending'
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $check_history_balance = $this->historybalance->total(array(
                'depositid' => $id,
                'type' => 'IN',
                'userid' => $row->userid
            ));

            if ($check_history_balance == 0) {
                $currentbalance = getCurrentBalance($row->userid, true);

                $balance = null;
                if ($row->isbonus > 0) {
                    $balance = $row->nominal + $row->nominalbonus - ($row->uniqueadmin ?? 0) - ($row->fee ?? 0);
                } else {
                    $balance = $row->nominal - ($row->uniqueadmin ?? 0) - ($row->fee ?? 0);
                }

                $inserthistorybalance = array();
                $inserthistorybalance['userid'] = $row->userid;
                $inserthistorybalance['type'] = 'IN';
                $inserthistorybalance['nominal'] = $balance;
                $inserthistorybalance['currentbalance'] = $currentbalance;
                $inserthistorybalance['depositid'] = $id;
                $inserthistorybalance['createdby'] = $row->userid;
                $inserthistorybalance['createddate'] = getCurrentDate();

                $this->historybalance->insert($inserthistorybalance);

                $balance = $currentbalance + $balance;

                $update = array();
                $update['balance'] = $balance;

                $this->msusers->update(array(
                    'id' => $row->userid
                ), $update);
            }

            $update = array();
            $update['status'] = 'Success';

            $this->deposits->update(array(
                'id' => $id
            ), $update);

            $this->send_notification($id, $row->merchantid, $row->phonenumber);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan konfirmasi');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dikonfirmasi');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_cancel_topup()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'a.merchantid' => getCurrentIdUser(),
                    'a.status' => 'Pending'
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $update = array();
            $update['status'] = 'Cancel';

            $this->deposits->update(array(
                'id' => $id
            ), $update);

            $this->send_notification($id, $row->merchantid, $row->phonenumber);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal membatalkan permitaan deposit');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Permintaan deposit berhasil dibatalkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function wrong_nominal()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->get(array(
                'id' => $id,
                'merchantid' => getCurrentIdUser(),
                'status' => 'Pending'
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('user/deposit/wrong_nominal', array(
                    'deposit' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_wrong_nominal()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $nominal = getPost('nominal');
            $oldnominal = $nominal;

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            } else if ($nominal == null) {
                throw new Exception('Nominal wajib diisi');
            } else if (!is_numeric($nominal)) {
                throw new Exception('Nominal harus berupa angka');
            } else if ($nominal <= 0) {
                throw new Exception('Nominal harus lebih dari 0');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'a.merchantid' => getCurrentIdUser(),
                    'a.status' => 'Pending'
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $getrowuser = $this->msusers->get(array(
                'id' => $row->userid
            ))->row();

            if ($getrowuser->balancelimit != null && $getrowuser->balancelimit > 0) {
                if (($getrowuser->balance + $nominal) > $getrowuser->balancelimit) {
                    throw new Exception('Anda melebihi batas Maksimal Penyimpanan Saldo, Jumlah saldo Maksimal yang dapat disimpan ' . IDR($getrowuser->balancelimit));
                }
            }

            $nominalbonus = 0;
            if ($row->isbonus && $row->paymentmethodid != null) {
                $paymentmethod = $this->paymentmethod->get(array(
                    'id' => $row->paymentmethodid,
                    'userid' => getCurrentIdUser()
                ))->row();

                if ($paymentmethod != null && $paymentmethod->isbonus == 1) {
                    if ($paymentmethod->bonustype == 'Nominal') {
                        $nominalbonus = $paymentmethod->nominalbonus;
                    } else if ($paymentmethod->bonustype == 'Persentase') {
                        $nominalbonus = $nominal * ($paymentmethod->nominalbonus / 100);
                    }
                }
            } else if ($row->isbonus && $row->percentagebonus != null) {
                $nominalbonus = $nominal * ($row->percentagebonus / 100);
            }

            $nominalfee = 0;
            if ($row->fee > 0) {
                if ($row->paymenttype == 'Otomatis') {
                    $paymentgateway = $this->paymentgateway->row(array(
                        'userid' => $row->merchantid,
                        'type' => 'Payment Gateway',
                        'vendor' => $row->gatewayvendor,
                    ));

                    if ($paymentgateway != null) {
                        $midtranspayment = getMidtransPayments();
                        $tripaypayment = getTripayPayments();

                        $paymentname = null;

                        if ($row->gatewayvendor == 'Midtrans') {
                            foreach ($midtranspayment as $key => $value) {
                                if ($value == $row->payment) {
                                    $paymentname = $key;
                                }
                            }
                        } elseif ($row->gatewayvendor == 'Tripay') {
                            foreach ($tripaypayment as $key => $value) {
                                if ($value == $row->payment) {
                                    $paymentname = $key;
                                }
                            }
                        }

                        if ($paymentname != null) {
                            $feepaymentgateway = $this->feepaymentgateway->row(array(
                                'paymentgatewayid' => $paymentgateway->id,
                                'paymentname' => $paymentname,
                            ));

                            if ($feepaymentgateway != null) {
                                if ($feepaymentgateway->feetype == 'Persentase') {
                                    $nominalfee = $nominal * ($feepaymentgateway->fee / 100);
                                } else if ($feepaymentgateway->feetype == 'Nominal') {
                                    $nominalfee = $feepaymentgateway->fee;
                                }
                            }
                        }
                    } else {
                        $paymentgateway = $this->paymentgateway->row(array(
                            'userid' => $row->merchantid,
                            'type' => $row->payment,
                        ));

                        if ($paymentgateway != null && $paymentgateway->feetype != null) {
                            if ($paymentgateway->feetype == 'Persentase') {
                                $nominalfee = $nominal * ($paymentgateway->nominalfee / 100);
                            } else if ($paymentgateway->feetype == 'Nominal') {
                                $nominalfee = $paymentgateway->nominalfee;
                            }
                        }
                    }
                } else if ($row->paymenttype == 'Manual') {
                    $paymentmethod = $this->paymentmethod->get(array(
                        'id' => $row->paymentmethodid,
                        'userid' => getCurrentIdUser()
                    ))->row();

                    if ($paymentmethod != null) {
                        if ($paymentmethod->feetype == 'Persentase') {
                            $nominalfee = $nominal * ($paymentmethod->nominalfee / 100);
                        } else if ($paymentmethod->feetype == 'Nominal') {
                            $nominalfee = $paymentmethod->nominalfee;
                        }
                    }
                }
            }

            $nominal = $nominal + $nominalbonus - $nominalfee;

            $update = array();
            $update['nominal'] = $oldnominal;
            $update['nominalbonus'] = $nominalbonus;
            $update['fee'] = $nominalfee;
            $update['uniqueadmin'] = 0;
            $update['status'] = 'Success';

            $this->deposits->update(array(
                'id' => $id
            ), $update);

            $this->send_notification($id, $row->merchantid, $row->phonenumber);

            $check_history_balance = $this->historybalance->total(array(
                'depositid' => $id,
                'type' => 'IN',
                'userid' => $row->userid
            ));

            if ($check_history_balance == 0) {
                $currentbalance = getCurrentBalance($row->userid, true);

                $inserthistorybalance = array();
                $inserthistorybalance['userid'] = $row->userid;
                $inserthistorybalance['type'] = 'IN';
                $inserthistorybalance['nominal'] = $nominal;
                $inserthistorybalance['currentbalance'] = $currentbalance;
                $inserthistorybalance['depositid'] = $id;
                $inserthistorybalance['createdby'] = $row->userid;
                $inserthistorybalance['createddate'] = getCurrentDate();

                $this->historybalance->insert($inserthistorybalance);

                $update = array();
                $update['balance'] = $currentbalance + $nominal;

                $this->msusers->update(array(
                    'id' => $row->userid
                ), $update);
            }

            $insert = array();
            $insert['depositid'] = $id;
            $insert['correctnominal'] = $oldnominal;
            $insert['recalculatebonus'] = $nominalbonus;
            $insert['recalculatefee'] = $nominalfee;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->historywrongnominal->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan konfirmasi');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dikonfirmasi');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
