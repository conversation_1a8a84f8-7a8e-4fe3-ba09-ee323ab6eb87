<?php
defined('BASEPATH') or die('No direct script access allowed!');

class LoginActivity extends MY_Model
{
    protected $table = 'loginactivity';

    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.createddate, a.ipaddress, a.useragent')
            ->from($this->table . ' a')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}
