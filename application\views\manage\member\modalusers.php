<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-xl">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Daftar Akses Member</h3>

            <a href="<?= base_url('manage/member/export/' . stringEncryption('encrypt', $member->id)) ?>" class="btn btn-success btn-sm me-3">
                <i class="fa fa-file-excel"></i> Export Excel
            </a>
        </div>

        <div class="modal-body">
            <table class="table table-striped table-row-bordered gy-5 datatables-users">
                <thead>
                    <tr class="fw-semibold fs-6 text-muted">
                        <th>Nama</th>
                        <th>Email</th>
                        <th>Nomor Handphone</th>
                        <th>Tanggal Transaksi Terakhir</th>
                        <th>Total Transaksi</th>
                        <th>Transaksi PPOB</th>
                        <th>Transaksi SMM</th>
                        <th><PERSON><PERSON><PERSON></th>
                        <th>Status Email</th>
                    </tr>
                </thead>

                <tbody>
                </tbody>
            </table>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
        </div>
    </div>
</div>

<script>
    $('.datatables-users').DataTable({
        responsive: true,
        processing: true,
        serverSide: true,
        stateSave: true,
        ordering: false,
        ajax: {
            url: '<?= base_url(uri_string() . '/datatables') ?>',
            method: 'POST',
            data: {
                merchantid: '<?= $member->id ?>'
            }
        },
    });
</script>