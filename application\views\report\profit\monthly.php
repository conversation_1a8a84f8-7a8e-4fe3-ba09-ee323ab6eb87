<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <div class="page-title d-flex flex-column me-3">
        <h1 class="d-flex text-dark fw-bold my-1 fs-3"><PERSON><PERSON><PERSON>unt<PERSON> Bulanan</h1>
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <li class="breadcrumb-item text-gray-600">La<PERSON><PERSON></li>
            <li class="breadcrumb-item text-gray-500">Keuntungan Bulanan</li>
        </ul>
    </div>
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <!-- Card dengan filter tahun -->
            <div class="accordion mb-4" id="kt_accordion_1">
                <div class="accordion-item">
                    <!-- HEADER Accordion -->
                    <h2 class="accordion-header" id="kt_accordion_1_header_1">
                        <button class="accordion-button fs-4 fw-semibold collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#kt_accordion_1_body_1" aria-expanded="false" aria-controls="kt_accordion_1_body_1">
                            Preview Grafik Keuntungan Bulanan
                        </button>
                    </h2>

                    <!-- BODY Accordion -->
                    <div id="kt_accordion_1_body_1" class="accordion-collapse collapse" aria-labelledby="kt_accordion_1_header_1" data-bs-parent="#kt_accordion_1">
                        <div class="card mb-5">
                            <div class="card-header d-flex justify-content-end align-items-center">
                                <label for="yearFilter" class="me-2 fw-semibold mb-0">Pilih Tahun:</label>
                                <select id="yearFilter" class="form-select w-auto">
                                    <?php
                                    $currentYear = date('Y');
                                    for ($y = $currentYear; $y >= $currentYear - 5; $y--): ?>
                                        <option value="<?= $y ?>" <?= $y == $currentYear ? 'selected' : '' ?>>
                                            <?= $y ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>

                            <div class="accordion-body">
                                <div id="kt_charts_widget_6_chart" style="height: 350px"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- DataTable -->
            <div class="card mb-5">
                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables-profit text-nowrap">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th>Bulan</th>
                                <th>Omset</th>
                                <th>Keuntungan (Terealisasi)</th>
                                <th>Keuntungan (Belum Terealisasi)</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>

                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- PATCH GLOBAL YANG AMAN -->
<script>
    if (!Object.prototype.put) {
        Object.defineProperty(Object.prototype, 'put', {
            value: function(key, val) {
                this[key] = val;
                return this;
            },
            enumerable: false
        });
    }
</script>

<!-- SCRIPT UTAMA -->
<script>
    // Fungsi untuk memuat chart berdasarkan tahun yang dipilih
    function loadChart(year) {
        const url = `<?= base_url('report/profit/chart_data_monthly') ?>?year=${year}`;

        fetch(url)
            .then(r => r.json())
            .then(res => {
                if (res.status !== 'SUCCESS') {
                    console.error('Chart data error:', res.message);
                    return;
                }
                const bulanNama = [
                    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
                    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
                ];

                const labels = res.data.map(x => bulanNama[parseInt(x.label, 10) - 1]);

                const realized = res.data.map(x => x.realized);
                const unrealized = res.data.map(x => x.unrealized);

                const options = {
                    series: [{
                            name: 'Keuntungan Terealisasi',
                            data: realized
                        },
                        {
                            name: 'Keuntungan Belum Terealisasi',
                            data: unrealized
                        }
                    ],
                    chart: {
                        type: 'area',
                        height: 350,
                        toolbar: {
                            show: false
                        }
                    },
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        curve: 'smooth'
                    },
                    xaxis: {
                        categories: labels
                    },
                    tooltip: {
                        y: {
                            formatter: val => 'Rp ' + new Intl.NumberFormat('id-ID').format(val)
                        }
                    }
                };

                const chartContainer = document.querySelector('#kt_charts_widget_6_chart');
                chartContainer.innerHTML = ""; // Hapus chart lama
                const chart = new ApexCharts(chartContainer, options);
                chart.render();
            })
            .catch(err => console.error('Chart Fetch Error:', err));
    }

    // Inisialisasi saat halaman dimuat
    window.onload = function() {
        const yearSelect = document.getElementById('yearFilter');

        // Inisialisasi DataTable dengan filter tahun
        const dtProfit = $('.datatables-profit').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url('report/profit/monthly/datatables') ?>',
                data: function(d) {
                    d.year = yearSelect.value;
                }
            }
        });

        // Load grafik awal
        loadChart(yearSelect.value);

        // Saat dropdown tahun diubah
        yearSelect.addEventListener('change', function() {
            const selectedYear = this.value;
            loadChart(selectedYear); // Reload grafik
            dtProfit.ajax.reload(); // Reload DataTables
        });
    };
</script>