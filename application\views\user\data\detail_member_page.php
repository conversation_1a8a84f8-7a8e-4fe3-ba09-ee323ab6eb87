<?php defined('BASEPATH') or die('No direct script access allowed!'); ?>

<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <div class="page-title d-flex flex-column me-3">
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Detail Member</h1>
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('users/data') ?>" class="text-gray-600 text-hover-primary">Data Pengguna</a>
            </li>
            <li class="breadcrumb-item text-gray-500">Detail Member</li>
        </ul>
    </div>

    <a href="<?= base_url('users/data') ?>" class="btn btn-danger fw-bold">Kembali</a>
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header">
                    <div class="card-title">
                        <h3>Statistik Member</h3>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- Summary Cards -->
                        <div class="col-md-12">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card mb-3 bg-light-success">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="symbol symbol-40px me-3">
                                                    <span class="symbol-label bg-success">
                                                        <i class="fa fa-usd fs-1 text-white"></i>
                                                    </span>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="fw-semibold text-gray-800 fs-6">Total Deposit Berhasil</div>
                                                    <div class="fw-bold text-success fs-3">Rp <?= IDR($memberStats['successful_deposits'] ?? 0) ?></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="card mb-3 bg-light-primary">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="symbol symbol-40px me-3">
                                                    <span class="symbol-label bg-primary">
                                                        <i class="fa fa-gift fs-1 text-white"></i>
                                                    </span>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="fw-semibold text-gray-800 fs-6">Total Bonus Deposit Diterima</div>
                                                    <div class="fw-bold text-primary fs-3">Rp <?= IDR($memberStats['bonus_received'] ?? 0) ?></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="card mb-3 bg-light-warning">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="symbol symbol-40px me-3">
                                                    <span class="symbol-label bg-warning">
                                                        <i class="fa fa-clock fs-1 text-white"></i>
                                                    </span>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="fw-semibold text-gray-800 fs-6">Rata-rata Waktu Proses</div>
                                                    <div class="fw-bold text-warning fs-3">
                                                        <?php
                                                        $avgSeconds = floatval($memberStats['avg_processing_time'] ?? 0);
                                                        if ($avgSeconds > 0) {
                                                            $totalSeconds = intval($avgSeconds);
                                                            $minutes = intval($totalSeconds / 60);
                                                            $seconds = $totalSeconds % 60;
                                                            echo $minutes . 'm ' . $seconds . 's';
                                                        } else {
                                                            echo '-';
                                                        }
                                                        ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="card mb-3 bg-light-info">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="symbol symbol-40px me-3">
                                                    <span class="symbol-label bg-info">
                                                        <i class="fa fa-list fs-1 text-white"></i>
                                                    </span>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="fw-semibold text-gray-800 fs-6">Total Kategori Dibeli</div>
                                                    <div class="fw-bold text-info fs-3"><?= count($memberStats['top_categories']) ?> Kategori</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <!-- Header: Judul + Buttons -->
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div class="card-title">
                        <h3>Detail Member</h3>
                    </div>

                    <div class="d-flex gap-2 flex-wrap align-items-center">
                        <button class="btn btn-warning btn-sm" onclick="decreaseBalance('<?= stringEncryption('encrypt', $user->id) ?>')">
                            <i class="fa fa-dollar me-1"></i> Kurangi Saldo
                        </button>

                        <button class="btn btn-info btn-sm" onclick="changeRole('<?= stringEncryption('encrypt', $user->id) ?>')">
                            <i class="fa fa-user me-1"></i> Ubah Hak Akses
                        </button>

                        <button class="btn btn-primary btn-sm" onclick="changePassword('<?= stringEncryption('encrypt', $user->id) ?>')">
                            <i class="fa fa-key me-1"></i> Ubah Password
                        </button>

                        <button class="btn btn-success btn-sm" onclick="changePIN('<?= stringEncryption('encrypt', $user->id) ?>')">
                            <i class="fa fa-key me-1"></i> Ubah PIN
                        </button>

                        <button class="btn btn-danger btn-sm" onclick="deleteAccount('<?= stringEncryption('encrypt', $user->id) ?>')">
                            <i class="fa fa-trash me-1"></i> Hapus
                        </button>
                    </div>
                </div>

                <!-- Body: Nav + Panes -->
                <div class="card-body pt-0">
                    <!-- Nav tabs -->
                    <ul class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x fs-6 fw-semibold mt-6 gap-2">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link text-active-primary d-flex align-items-center pb-4 active show"
                                data-bs-toggle="tab" href="#detail-tab" aria-selected="true" role="tab">
                                <i class="ki-duotone ki-calendar-8 fs-4 me-1"><span
                                        class="svg-icon svg-icon-muted svg-icon-2x"><svg width="18" height="18"
                                            viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path opacity="0.3"
                                                d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z"
                                                fill="currentColor" />
                                            <path
                                                d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z"
                                                fill="currentColor" />
                                            <rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor" />
                                        </svg>
                                    </span>
                                </i> Detail
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link text-active-primary d-flex align-items-center pb-4" data-bs-toggle="tab"
                                href="#mutasi-tab">
                                <i class="ki-duotone ki-calendar-8 fs-4 me-1"><span class="path1"></span><span
                                        class="path2">
                                        <span class="svg-icon svg-icon-muted svg-icon-2x"><svg width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.3"
                                                    d="M11 11H13C13.6 11 14 11.4 14 12V21H10V12C10 11.4 10.4 11 11 11ZM16 3V21H20V3C20 2.4 19.6 2 19 2H17C16.4 2 16 2.4 16 3Z"
                                                    fill="currentColor" />
                                                <path
                                                    d="M21 20H8V16C8 15.4 7.6 15 7 15H5C4.4 15 4 15.4 4 16V20H3C2.4 20 2 20.4 2 21C2 21.6 2.4 22 3 22H21C21.6 22 22 21.6 22 21C22 20.4 21.6 20 21 20Z"
                                                    fill="currentColor" />
                                            </svg>
                                        </span>
                                </i> Mutasi Saldo
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link text-active-primary d-flex align-items-center pb-4" data-bs-toggle="tab"
                                href="#riwayat-transaksi-tab">
                                <i class="ki-duotone ki-bill fs-4 me-1">
                                    <!--begin::Svg Icon | path: /var/www/preview.keenthemes.com/keenthemes/metronic/docs/core/html/src/media/icons/duotune/ecommerce/ecm001.svg-->
                                    <span class="svg-icon svg-icon-muted svg-icon-2x"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path opacity="0.3" d="M18.041 22.041C18.5932 22.041 19.041 21.5932 19.041 21.041C19.041 20.4887 18.5932 20.041 18.041 20.041C17.4887 20.041 17.041 20.4887 17.041 21.041C17.041 21.5932 17.4887 22.041 18.041 22.041Z" fill="currentColor" />
                                            <path opacity="0.3" d="M6.04095 22.041C6.59324 22.041 7.04095 21.5932 7.04095 21.041C7.04095 20.4887 6.59324 20.041 6.04095 20.041C5.48867 20.041 5.04095 20.4887 5.04095 21.041C5.04095 21.5932 5.48867 22.041 6.04095 22.041Z" fill="currentColor" />
                                            <path opacity="0.3" d="M7.04095 16.041L19.1409 15.1409C19.7409 15.1409 20.141 14.7409 20.341 14.1409L21.7409 8.34094C21.9409 7.64094 21.4409 7.04095 20.7409 7.04095H5.44095L7.04095 16.041Z" fill="currentColor" />
                                            <path d="M19.041 20.041H5.04096C4.74096 20.041 4.34095 19.841 4.14095 19.541C3.94095 19.241 3.94095 18.841 4.14095 18.541L6.04096 14.841L4.14095 4.64095L2.54096 3.84096C2.04096 3.64096 1.84095 3.04097 2.14095 2.54097C2.34095 2.04097 2.94096 1.84095 3.44096 2.14095L5.44096 3.14095C5.74096 3.24095 5.94096 3.54096 5.94096 3.84096L7.94096 14.841C7.94096 15.041 7.94095 15.241 7.84095 15.441L6.54096 18.041H19.041C19.641 18.041 20.041 18.441 20.041 19.041C20.041 19.641 19.641 20.041 19.041 20.041Z" fill="currentColor" />
                                        </svg>
                                    </span>
                                    <!--end::Svg Icon-->
                                </i>
                                Riwayat Transaksi
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link text-active-primary d-flex align-items-center pb-4" data-bs-toggle="tab"
                                href="#riwayat-login-tab">
                                <i class="ki-duotone ki-clock fs-4 me-1">
                                    <span class="svg-icon svg-icon-muted svg-icon-2x">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2" />
                                            <path d="M12 7V12L15 15" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                    </span>
                                </i>
                                Riwayat Login
                            </a>
                        </li>
                    </ul>

                    <!-- Tab content -->
                    <div class="tab-content">
                        <!-- Detail Pane -->
                        <div class="tab-pane fade show active mt-6" id="detail-tab" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <!-- Semua field detail sisi kiri -->
                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Nomor Handphone</label>
                                        <div class="fw-bold fs-6 text-gray-800"><?= $user->phonenumber ?? '-' ?></div>
                                    </div>

                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Status Email</label>
                                        <div class="fw-bold fs-6 text-gray-800">
                                            <?php if ($user->isemailverified == 1): ?>
                                                <span class="badge badge-success">Terverifikasi</span>
                                            <?php else: ?>
                                                <span class="badge badge-danger">Belum Diverifikasi</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Saldo (Balance)</label>
                                        <div class="fw-bold fs-6 text-gray-800">Rp <?= IDR($user->balance) ?></div>
                                    </div>

                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Transaksi Terakhir</label>
                                        <div class="fw-bold fs-6 text-gray-800">
                                            <?= $lastTransaction ? DateFormat($lastTransaction->createddate, 'd F Y H:i:s') : '-' ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- Semua field detail sisi kanan -->
                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Nama</label>
                                        <div class="fw-bold fs-6 text-gray-800">
                                            <?= $user->name ?>
                                        </div>
                                    </div>

                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Email</label>
                                        <div class="fw-bold fs-6 text-gray-800"><?= $user->email ?></div>
                                    </div>

                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Hak Akses</label>
                                        <div class="fw-bold fs-6 text-gray-800">
                                            <span class="badge badge-primary "><?= $user->rolename ?? 'Member' ?></span>
                                        </div>
                                    </div>

                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Total Transaksi</label>
                                        <div class="fw-bold fs-6 text-gray-800"><?= IDR($user->transactiontotal ?? 0) ?> Transaksi</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Mutasi Saldo Pane -->
                        <div class="tab-pane fade" id="mutasi-tab" role="tabpanel">
                            <div class="card">
                                <div class="table-responsive">
                                    <table class="table table-striped table-row-bordered gy-5 datatables-mutasi-saldo">
                                        <thead>
                                            <tr class="fw-semibold fs-6 text-muted">
                                                <th>Tanggal</th>
                                                <th>Tipe</th>
                                                <th>Jumlah</th>
                                                <th>Saldo Sebelum</th>
                                                <th>Saldo Sesudah</th>
                                                <th>Kode Transaksi</th>
                                                <th>Kode Deposit</th>
                                                <th>Deskripsi</th>
                                            </tr>
                                        </thead>

                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Riwayat Transaksi Pane -->
                        <div class="tab-pane fade" id="riwayat-transaksi-tab" role="tabpanel">
                            <div class="card">
                                <div class="card-header p-0">
                                    <div class="card-title m-0">
                                        <ul class="nav nav-tabs nav-line-tabs nav-line-tabs-2x nav-stretch fs-6">
                                            <li class="nav-item">
                                                <a class="nav-link active" data-bs-toggle="tab" href="#transaksi-ppob-tab">
                                                    <i class="ki-duotone ki-bill fs-4 me-1">
                                                        <span class="path1"></span>
                                                        <span class="path2"></span>
                                                    </i>
                                                    PPOB
                                                </a>
                                            </li>

                                            <li class="nav-item">
                                                <a class="nav-link" data-bs-toggle="tab" href="#transaksi-smm-tab">
                                                    <i class="ki-duotone ki-chart-line fs-4 me-1">
                                                        <span class="path1"></span>
                                                        <span class="path2"></span>
                                                    </i>
                                                    SMM
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="card-body p-0">
                                    <div class="tab-content">
                                        <!-- PPOB Tab -->
                                        <div class="tab-pane fade show active" id="transaksi-ppob-tab" role="tabpanel">
                                            <div class="table-responsive">
                                                <table class="table table-striped table-row-bordered gy-5 datatables-riwayat-transaksi-ppob">
                                                    <thead>
                                                        <tr class="fw-semibold fs-6 text-muted">
                                                            <th>Kode Transaksi</th>
                                                            <th>Tanggal</th>
                                                            <th>Produk</th>
                                                            <th>Target</th>
                                                            <th>Harga</th>
                                                            <th>Status</th>
                                                            <th>SN</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody></tbody>
                                                </table>
                                            </div>
                                        </div>

                                        <!-- SMM Tab -->
                                        <div class="tab-pane fade" id="transaksi-smm-tab" role="tabpanel">
                                            <div class="table-responsive">
                                                <table class="table table-striped table-row-bordered gy-5 datatables-riwayat-transaksi-smm">
                                                    <thead>
                                                        <tr class="fw-semibold fs-6 text-muted">
                                                            <th>Kode Transaksi</th>
                                                            <th>Tanggal</th>
                                                            <th>Produk</th>
                                                            <th>Target</th>
                                                            <th>Jumlah</th>
                                                            <th>Harga</th>
                                                            <th>Status</th>
                                                            <th>Start Count</th>
                                                            <th>Remain</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody></tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Riwayat Login Pane -->
                        <div class="tab-pane fade" id="riwayat-login-tab" role="tabpanel">
                            <div class="card">
                                <div class="table-responsive">
                                    <table class="table table-striped table-row-bordered gy-5 datatables-riwayat-login">
                                        <thead>
                                            <tr class="fw-semibold fs-6 text-muted">
                                                <th>Tanggal</th>
                                                <th>IP Address</th>
                                                <th>User Agent</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> <!-- /.tab-content -->
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-3">
                <div class="card-header">
                    <div class="card-title">
                        <h4 class="fw-bold text-gray-800">Top 10 Kategori Sering Dibeli</h4>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($memberStats['top_categories'])): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-row-bordered gy-5 datatables">
                                <thead>
                                    <tr class="fw-semibold fs-6 text-muted">
                                        <th>No</th>
                                        <th>Kategori</th>
                                        <th>Total Order</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <?php foreach ($memberStats['top_categories'] as $index => $category): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td class="fw-bold text-gray-800"><?= $category->category ?? '-' ?></td>
                                            <td>
                                                <span class="badge badge-light-primary"><?= $category->total_orders ?> kali</span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <div class="text-muted">Belum ada data kategori yang dibeli</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <h4 class="fw-bold text-gray-800">Top 10 Produk Sering Dibeli</h4>
                    </div>
                </div>

                <div class="card-body pt-0">
                    <?php if (!empty($memberStats['top_products'])): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-row-bordered gy-5 datatables">
                                <thead>
                                    <tr class="fw-semibold fs-6 text-muted">
                                        <th>No</th>
                                        <th>Produk</th>
                                        <th>Kategori</th>
                                        <th>Total Order</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <?php foreach ($memberStats['top_products'] as $index => $product): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td class="fw-bold text-gray-800"><?= $product->productname ?? $product->productname_order ?></td>
                                            <td class="text-muted"><?= $product->category ?? '-' ?></td>
                                            <td>
                                                <span class="badge badge-light-success"><?= $product->total_orders ?> kali</span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <div class="text-muted">Belum ada data produk yang dibeli</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let mutasiTableInitialized = false;
    let riwayatTransaksiPpobTableInitialized = false;
    let riwayatTransaksiSmmTableInitialized = false;
    let riwayatLoginTableInitialized = false;

    // Initialize DataTable for Mutasi Saldo when tab is shown
    $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
        if (e.target.getAttribute('href') === '#mutasi-tab' && !mutasiTableInitialized) {
            $('.datatables-mutasi-saldo').DataTable({
                responsive: true,
                processing: true,
                serverSide: true,
                stateSave: true,
                ordering: false,
                ajax: {
                    url: '<?= base_url('users/data/history/balance/datatables') ?>',
                    method: 'POST',
                    data: {
                        id: '<?= $encrypted_id ?>'
                    }
                },
                columns: [{
                        data: 0
                    }, // Tanggal
                    {
                        data: 1
                    }, // Tipe
                    {
                        data: 2
                    }, // Jumlah
                    {
                        data: 3
                    }, // Saldo Sebelum
                    {
                        data: 4
                    }, // Saldo Sesudah
                    {
                        data: 5
                    }, // Kode Transaksi
                    {
                        data: 6
                    }, // Kode Deposit
                    {
                        data: 7
                    } // Deskripsi
                ]
            });
            mutasiTableInitialized = true;
        }

        // Initialize DataTable for Riwayat Transaksi PPOB when tab is shown
        if (e.target.getAttribute('href') === '#riwayat-transaksi-tab' && !riwayatTransaksiPpobTableInitialized) {
            $('.datatables-riwayat-transaksi-ppob').DataTable({
                responsive: true,
                processing: true,
                serverSide: true,
                stateSave: true,
                ordering: false,
                ajax: {
                    url: '<?= base_url('users/data/history/transaction/ppob/datatables') ?>',
                    method: 'POST',
                    data: {
                        id: '<?= $encrypted_id ?>'
                    }
                },
                columns: [{
                        data: 0
                    }, // Kode Transaksi
                    {
                        data: 1
                    }, // Tanggal
                    {
                        data: 2
                    }, // Produk
                    {
                        data: 3
                    }, // Target
                    {
                        data: 4
                    }, // Harga
                    {
                        data: 5
                    }, // Status
                    {
                        data: 6
                    } // SN
                ],
                language: {
                    processing: "Memuat data...",
                    emptyTable: "Tidak ada data riwayat transaksi PPOB",
                    zeroRecords: "Tidak ada data yang cocok"
                }
            });
            riwayatTransaksiPpobTableInitialized = true;
        }

        // Initialize DataTable for Riwayat Login when tab is shown
        if (e.target.getAttribute('href') === '#riwayat-login-tab' && !riwayatLoginTableInitialized) {
            $('.datatables-riwayat-login').DataTable({
                responsive: true,
                processing: true,
                serverSide: true,
                stateSave: true,
                ordering: false,
                ajax: {
                    url: '<?= base_url('users/data/history/login/datatables') ?>',
                    method: 'POST',
                    data: {
                        id: '<?= $encrypted_id ?>'
                    },
                    error: function(xhr, error, code) {
                        console.log('DataTable Error:', xhr, error, code);
                        console.log('Response Text:', xhr.responseText);
                    }
                },
                columns: [{
                        data: 0
                    }, // Tanggal
                    {
                        data: 1
                    }, // IP Address
                    {
                        data: 2
                    } // User Agent
                ],
                language: {
                    processing: "Memuat data...",
                    emptyTable: "Tidak ada data riwayat login",
                    zeroRecords: "Tidak ada data yang cocok"
                }
            });
            riwayatLoginTableInitialized = true;
        }
    });

    // Initialize DataTable for SMM sub-tab when shown
    $('a[href="#transaksi-smm-tab"]').on('shown.bs.tab', function(e) {
        if (!riwayatTransaksiSmmTableInitialized) {
            $('.datatables-riwayat-transaksi-smm').DataTable({
                responsive: true,
                processing: true,
                serverSide: true,
                stateSave: true,
                ordering: false,
                ajax: {
                    url: '<?= base_url('users/data/history/transaction/smm/datatables') ?>',
                    method: 'POST',
                    data: {
                        id: '<?= $encrypted_id ?>'
                    }
                },
                columns: [{
                        data: 0
                    }, // Kode Transaksi
                    {
                        data: 1
                    }, // Tanggal
                    {
                        data: 2
                    }, // Produk
                    {
                        data: 3
                    }, // Target
                    {
                        data: 4
                    }, // Jumlah
                    {
                        data: 5
                    }, // Harga
                    {
                        data: 6
                    }, // Status
                    {
                        data: 7
                    }, // Start Count
                    {
                        data: 8
                    } // Remain
                ],
                language: {
                    processing: "Memuat data...",
                    emptyTable: "Tidak ada data riwayat transaksi SMM",
                    zeroRecords: "Tidak ada data yang cocok"
                }
            });
            riwayatTransaksiSmmTableInitialized = true;
        }
    });

    // Functions for actions
    function changePIN(id) {
        $.ajax({
            url: '<?= base_url('users/data/change/pin') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }

    function changePassword(id) {
        $.ajax({
            url: '<?= base_url('users/data/change/password') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }

    function decreaseBalance(id) {
        $.ajax({
            url: '<?= base_url('users/data/decrease/balance') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        })
    }

    function changeRole(id) {
        $.ajax({
            url: '<?= base_url('users/data/change/role') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        })
    }

    function deleteAccount(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Pengguna akan dihapus, pengguna akan menerima pesan akun telah dihapus pada saat login',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url('users/data/delete') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.href =
                                    '<?= base_url('users/data') ?>';
                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                });
            }
        });
    }
</script>