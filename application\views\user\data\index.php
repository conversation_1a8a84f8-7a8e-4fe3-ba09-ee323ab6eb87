<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Data Pengguna</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Pengguna</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Data Pengguna</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <div>
        <!--begin::Button-->
        <a href="<?= base_url(uri_string() . '/deleted') ?>" class="btn btn-danger fw-bold">Pengguna Dihapus</a>
        <a href="javascript:;" onclick="addUser()" class="btn btn-dark fw-bold">Tambah Pengguna</a>
        <a href="<?= base_url(uri_string() . '/export') ?>" class="btn btn-success fw-bold">
            Export
        </a>
        <!--end::Button-->
    </div>
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="email" class="col-form-label fw-semibold fs-6 pt-0">Email</label>
                                <input type="email" name="email" id="email" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Email" autocomplete="off">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="emailstatus" class="col-form-label fw-semibold fs-6 pt-0">Status Email</label>
                                <select name="emailstatus" id="emailstatus" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                    <option value="">Pilih Status</option>
                                    <option value="T">Terverifikasi</option>
                                    <option value="F">Belum Terverifikasi</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="mb-7">
                                <label for="role" class="col-form-label fw-semibold fs-6 pt-0">Level Akses</label>
                                <select name="role" id="role" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0">
                                    <option value="">Pilih Level Akses</option>
                                    <?php foreach ($role as $val) : ?>
                                        <option value="<?= $val->id ?>"><?= $val->rolename ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <button type="button" class="btn btn-dark w-100" onclick="filter()">Filter</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-5">
                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables-users">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th>Level Akses</th>
                                <th>Nama</th>
                                <th>Nomor Handphone</th>
                                <th>Jumlah Saldo</th>
                                <th>Status</th>
                                <th>Transaksi Terakhir</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>

                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('.datatables-users').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
            },
            initComplete: function(settings, json) {
                KTMenu.init();
            },
            drawCallback: function(settings) {
                KTMenu.init();
            }
        }).on('click', 'td.dtr-control', function() {
            KTMenu.init();
        });
    };

    function addUser() {
        $.ajax({
            url: '<?= base_url(uri_string() . '/add/user') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }

    function verifikasiEmail(id) {
        return Swal.fire({
            title: 'Apakah anda yakin?',
            text: 'Anda akan memverifikasi email akun pengguna ini',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/verifikasiemail') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();

                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function filter() {
        let email = $('input[name="email"]').val();
        let emailstatus = $('#emailstatus').val();
        let role = $('#role').val();

        $('.datatables-users').DataTable().destroy();
        $('.datatables-users').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url('users/data/datatables') ?>',
                method: 'POST',
                data: {
                    email: email,
                    status: emailstatus,
                    role: role,
                }
            },
            initComplete: function(settings, json) {
                KTMenu.init();
            },
            drawCallback: function(settings) {
                KTMenu.init();
            }
        }).on('click', 'td.dtr-control', function() {
            KTMenu.init();
        });
    }

    function detailMember(id) {
        // Redirect to detail member page
        window.location.href = '<?= base_url('users/data/detail/member/') ?>' + id;
    }
</script>