<?php

use Duitku\Api;
use iPaymu\iPaymu;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;
use Midtrans\Config;
use Midtrans\Snap;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsProduct $msproduct
 * @property DisabledCategory $disabledcategory
 * @property Datatables $datatables
 * @property MsRole $msrole
 * @property MsRoleDiscount $msrolediscount
 * @property MsRoleDiscountAdv $msrolediscountadv
 * @property MsUsers $msusers
 * @property TrOrder $trorder
 * @property HistoryBalance $historybalance
 * @property DisabledBrand $disabledbrand
 * @property DisabledCategory $disabledcategory
 * @property BrandImage $brandimage
 * @property MsIcons $msicons
 * @property MsSlider $msslider
 * @property CI_DB_mysqli_driver $db
 * @property MsPlatformSosmed $msplatformsosmed
 * @property MsDetailPlatform $msdetailplatform
 * @property MsPaymentGateway $mspaymentgateway
 * @property MsPaymentMethod $mspaymentmethod
 * @property UserBuyNotificationHandler $userbuynotificationhandler
 * @property FeePaymentGateway $feepaymentgateway
 * @property HistoryBalance $historybalance
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 * @property ApiKeys $apikeys
 * @property MsVendorDetail $msvendordetail
 * @property MsStockproduct $msstockproduct
 * @property MsStockproductDetail $msstockproductdetail
 * @property MsCustomerSupport $mscustomersupport
 */
class Landing extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('DisabledCategory', 'disabledcategory');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('MsRoleDiscount', 'msrolediscount');
        $this->load->model('MsRoleDiscountAdv', 'msrolediscountadv');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('TrOrder', 'trorder');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('DisabledBrand', 'disabledbrand');
        $this->load->model('DisabledCategory', 'disabledcategory');
        $this->load->model('BrandImage', 'brandimage');
        $this->load->model('MsIcons', 'msicons');
        $this->load->model('MsSlider', 'msslider');
        $this->load->model('MsPlatformSosmed', 'msplatformsosmed');
        $this->load->model('MsDetailPlatform', 'msdetailplatform');
        $this->load->model('MsPaymentGateway', 'mspaymentgateway');
        $this->load->model('MsPaymentMethod', 'mspaymentmethod');
        $this->load->model('UserBuyNotificationHandler', 'userbuynotificationhandler');
        $this->load->model('FeePaymentGateway', 'feepaymentgateway');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
        $this->load->model('ApiKeys', 'apikeys');
        $this->load->model('MsVendorDetail', 'msvendordetail');
        $this->load->model('MsStockproduct', 'msstockproduct');
        $this->load->model('MsStockproductDetail', 'msstockproductdetail');
        $this->load->model('MsCustomerSupport', 'mscustomersupport');
    }

    public function index()
    {
        $data = array();
        $data['title'] = $this->merchant->companyname;

        if (getCurrentThemeConfiguration($this->merchant->id) == 'Fin-App') {
            $data['content'] = 'landing/index';
            $data['slider'] = $this->msslider->result(array(
                'a.userid' => $this->merchant->id
            ));

            $merchantid = $this->merchant->id;

            $last_transaction = $this->trorder->select('b.name, c.productname, a.price')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
                ->order_by('a.createddate', 'DESC')
                ->limit(10)
                ->result(array(
                    "(b.merchantid = '$merchantid' OR a.merchantid_order = '$merchantid') =" => true,
                ));

            $marquee = "";
            foreach ($last_transaction as $key => $value) {
                $name = $value->name;
                $name = sensorKarakter($name, 5);

                $marquee .=  $name . " Telah melakukan transaksi " . $value->productname . " senilai Rp " . number_format($value->price, 0, ',', '.') . " | ";
            }

            $data['marquee'] = $marquee;

            $data['customersupport'] = $this->mscustomersupport->get(array(
                'createdby' => $this->merchant->id
            ));

            return viewTemplate($this->merchant->id, "master", $data);
        } else if (getCurrentThemeConfiguration($this->merchant->id) == 'Sobat-Serverppob') {
            $data['content'] = 'landing/index';

            return viewTemplate($this->merchant->id, "master", $data);
        } else {
            $data['content'] = 'landing/index';

            return $this->load->view('landing/master', $data);
        }
    }

    public function privacypolicy()
    {
        if (getCurrentThemeConfiguration($this->merchant->id) == 'Fin-App') {
            return show_404();
        }

        $privacypolicy = null;
        if (file_exists('./../../application/templates/privacypolicy_member.txt')) {
            $privacypolicy = file_get_contents('./../../application/templates/privacypolicy_member.txt');
        }

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Kebijakan Privasi';
        $data['privacypolicy'] = str_replace('${companyname}', $this->merchant->companyname, $privacypolicy);
        if (getCurrentThemeConfiguration($this->merchant->id) == 'Sobat-Serverppob') {
            $data['content'] = 'landing/privacypolicy';

            return viewTemplate($this->merchant->id, "master", $data);
        } else {
            $data['content'] = 'landing/privacypolicy';

            return $this->load->view('landing/master', $data);
        }
    }

    public function digital()
    {
        if (getCurrentThemeConfiguration($this->merchant->id) == 'Fin-App') {
            return show_404();
        }

        if ($this->merchant->companycategory != 'PPOB' && $this->merchant->companycategory != 'PPOB & SMM') {
            return redirect(base_url('?userid=' . $this->userid));
        }

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.category_apikey' => 'PPOB',
        );

        if ($this->merchant->multivendor != 1) {
            $ppob = getCurrentVendor('PPOB', $this->merchant->id);
            $where["(a.vendor = '$ppob' OR a.vendor IS NULL) ="] = true;
            $where['a.vendorid'] = null;
        } else {
            $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
        }

        $category = $this->msproduct->select('a.category')
            ->where_not_in('a.category', $disabled_category)
            ->group_by('a.category')
            ->order_by('a.category', 'ASC')
            ->result($where);

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Produk Digital';
        $data['category'] = $category;
        $data['content'] = 'landing/digital';

        return $this->load->view('landing/master', $data);
    }

    public function datatables_digital()
    {
        if (getCurrentThemeConfiguration($this->merchant->id) == 'Fin-App') {
            return show_404();
        }

        $category = getPost('category');
        $brand = getPost('brand');

        $data = array();

        $datatable = $this->datatables->make('MsProduct', 'QueryDatatables', 'SearchDatatables');

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.category_apikey' => 'PPOB',
            'b.id' => null,
            'a.status' => 1
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where["(a.vendor = '$vendor' OR a.vendor IS NULL) ="] = true;
            $where['a.vendorid'] = null;
        } else {
            $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
        }

        if ($category != null) {
            $where['a.category'] = $category;
        }

        if ($brand != null) {
            $where['a.brand'] = $brand;
        }

        $currentuser = getCurrentUser();

        if (($currentuser->roleid ?? null) == null) {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'isdefault' => '1',
            ))->row();
        } else {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'id' => $currentuser->roleid
            ))->row();
        }

        if ($getrole != null) {
            if ($getrole->discounttype == 'Simple') {
                $getrolediscount = $this->msrolediscount->get(array(
                    'userid' => $this->merchant->id,
                    'roleid' => $getrole->id,
                ))->row();

                $discount = $getrolediscount->trxdiscount ?? 0;
            } else {
                $discount = $this->msrolediscountadv->get(array(
                    'createdby' => $this->merchant->id,
                    'roleid' => $getrole->id,
                    "(servicetype = 'Prabayar' OR servicetype = 'Pascabayar') =" => true
                ))->result();
            }
        } else {
            $getrole = new stdClass();
            $getrole->discounttype = 'Simple';
            $discount = 0;
        }

        foreach ($datatable->getData($where) as $key => $value) {
            if ($value->status == 1) {
                $status = "<span class=\"badge badge-success\">Normal</span>";
            } else {
                $status = "<span class=\"badge badge-danger\">Gangguan</span>";
            }

            $detail = array();
            $detail[] = $value->productname;
            $detail[] = $value->category;
            $detail[] = $value->brand;
            $detail[] = $value->description ?? '-';

            if ($value->subcategory_apikey == 'PRABAYAR') {
                if ($getrole->discounttype == 'Simple') {
                    $detail[] = IDR($value->price - $discount);
                } else {
                    $found = false;

                    foreach ($discount as $val) {
                        if ($found) continue;

                        if ($val->startrange <= $value->price && $val->endrange >= $value->price && strtoupper($val->servicetype) == 'PRABAYAR') {
                            if ($val->discounttype == 'Persentase') {
                                $detail[] = IDR($value->price - ($value->price * $val->nominal / 100));

                                $found = true;
                            } else {
                                $detail[] = IDR($value->price - $val->nominal);

                                $found = true;
                            }
                        }
                    }

                    if ($found == false) {
                        $detail[] = IDR($value->price);
                    }
                }
            } else {
                if ($getrole->discounttype == 'Simple') {
                    $detail[] = IDR($value->admin + $value->profit - $discount);
                } else {
                    $found = false;

                    foreach ($discount as $val) {
                        if ($found) continue;

                        if ($val->startrange <= $value->admin + $value->profit && $val->endrange >= $value->admin + $value->profit && strtoupper($val->servicetype) == 'PASCABAYAR') {
                            if ($val->discounttype == 'Persentase') {
                                $detail[] = IDR($value->admin + $value->profit - ($value->admin + $value->profit * $val->nominal / 100));

                                $found = true;
                            } else {
                                $detail[] = IDR($value->admin + $value->profit - $val->nominal);

                                $found = true;
                            }
                        }
                    }

                    if ($found == false) {
                        $detail[] = IDR($value->admin + $value->profit);
                    }
                }
            }

            $detail[] = $status;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function smm()
    {
        if (getCurrentThemeConfiguration($this->merchant->id) == 'Fin-App') {
            return show_404();
        }

        if ($this->merchant->companycategory != 'SMM' && $this->merchant->companycategory != 'PPOB & SMM') {
            return redirect(base_url('?userid=' . $this->userid));
        }

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'SMM'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.category_apikey' => 'SMM',
        );

        if ($this->merchant->multivendor != 1) {
            $smm = getCurrentVendor('SMM', $this->merchant->id);
            $where["(a.vendor = '$smm' OR a.vendor IS NULL) ="] = true;
            $where['a.vendorid'] = null;
        } else {
            $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
        }

        $category = $this->msproduct->select('a.category')
            ->where_not_in('a.category', $disabled_category)
            ->group_by('a.category')
            ->order_by('a.category', 'ASC')
            ->result($where);

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Produk SMM';
        $data['category'] = $category;
        $data['content'] = 'landing/smm';

        return $this->load->view('landing/master', $data);
    }

    public function datatables_smm()
    {
        if (getCurrentThemeConfiguration($this->merchant->id) == 'Fin-App') {
            return show_404();
        }

        $category = getPost('category');

        $data = array();

        $datatable = $this->datatables->make('MsProduct', 'QueryDatatables', 'SearchDatatables');

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.category_apikey' => 'SMM',
            'a.subcategory_apikey' => 'SMM',
            'b.id' => null,
            'a.status' => 1,
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('SMM', $this->merchant->id);
            $where["(a.vendor = '$vendor' OR a.vendor IS NULL) ="] = true;
            $where['a.vendorid'] = null;
        } else {
            $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
        }

        if ($category != null) {
            $where['a.category'] = $category;
        } else {
            $where['a.category !='] = null;
        }

        $currentuser = getCurrentUser();

        if (($currentuser->roleid ?? null) == null) {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'isdefault' => '1'
            ))->row();
        } else {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'id' => $currentuser->roleid
            ))->row();
        }

        if ($getrole != null) {
            if ($getrole->discounttype == 'Simple') {
                $getrolediscount = $this->msrolediscount->get(array(
                    'userid' => $this->merchant->id,
                    'roleid' => $getrole->id
                ))->row();

                $discount = $getrolediscount->trxdiscount ?? 0;
            } else {
                $discount = $this->msrolediscountadv->get(array(
                    'createdby' => $this->merchant->id,
                    'roleid' => $getrole->id,
                    'servicetype' => 'SMM'
                ))->result();
            }
        } else {
            $getrole = new stdClass();
            $getrole->discounttype = 'Simple';
            $discount = 0;
        }

        foreach ($datatable->getData($where) as $key => $value) {
            if ($value->status == 1) {
                $status = "<span class=\"badge badge-success\">Normal</span>";
            } else {
                $status = "<span class=\"badge badge-danger\">Gangguan</span>";
            }

            $detail = array();
            $detail[] = $value->productname;
            $detail[] = $value->category;
            $detail[] = IDR($value->minorder);
            $detail[] = IDR($value->maxorder);

            if ($getrole->discounttype == 'Simple') {
                $detail[] = IDR($value->price - $discount);
            } else {
                $found = false;

                foreach ($discount as $val) {
                    if ($found) continue;

                    if ($val->startrange <= $value->price && $val->endrange >= $value->price) {
                        if ($val->discounttype == 'Persentase') {
                            $detail[] = IDR($value->price - ($value->price * $val->nominal / 100));

                            $found = true;
                        } else {
                            $detail[] = IDR($value->price - $val->nominal);

                            $found = true;
                        }
                    }
                }

                if ($found == false) {
                    $detail[] = IDR($value->price);
                }
            }

            $detail[] = $status;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function maintenance()
    {
        if ($this->merchant->ismaintenance != 1) {
            if (readConfig('./../../ai9iSFg3aEdUMmFxb1hZOGJVK2pvZz09.cfg', 'maintenance') != 'true') {
                return redirect(base_url('?userid=' . $this->userid));
            }
        }

        return $this->load->view('maintenance');
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        // Kirim Firebase notification untuk order
        sendFirebaseNotificationOrder($orderid, $userid);

        $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
            'userid' => $userid,
        ));

        if ($apikeys_whatsapp->num_rows() == 0) {
            return false;
        }

        $row = $apikeys_whatsapp->row();

        $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

        $messagenotification = replaceParameterNotification($orderid, $userid);
        if ($messagenotification != null && $phonenumber != null) {
            $phonenumber = changePrefixPhone($phonenumber);

            $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

            if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
            }

            return true;
        }

        return false;
    }

    public function ppob_category()
    {
        if (getCurrentThemeConfiguration($this->merchant->id) != 'Fin-App' && getCurrentThemeConfiguration($this->merchant->id) != 'Sobat-Serverppob') {
            return show_404();
        }

        if ($this->merchant->companycategory != 'PPOB' && $this->merchant->companycategory != 'PPOB & SMM') {
            return JSONResponse(array(
                'RESULT' => 'ERROR',
                'MESSAGE' => 'Anda tidak memiliki akses ke halaman ini.'
            ));
        }

        $current_vendor = getCurrentVendor('PPOB', $this->merchant->id);

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB',
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.category_apikey' => 'PPOB',
            'a.subcategory_apikey' => 'PRABAYAR',
            'a.category !=' => null,
        );

        if ($this->merchant->multivendor != 1) {
            $current_vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where["(a.vendor = '$current_vendor' OR a.vendor IS NULL) ="] = true;
            $where['a.vendorid'] = null;
        } else {
            $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
        }

        $this->db->select('a.category, c.asseturl')
            ->from('msproduct a')
            ->join('categoryimage b', 'b.categoryname = a.category AND b.userid = a.userid')
            ->join('msicons c', 'c.id = b.assetid')
            ->join('brandimage d', 'd.brandname = a.brand AND d.userid = a.userid AND d.categoryname = a.category')
            ->where($where)
            ->group_by('a.category')
            ->order_by('a.category', 'ASC');

        if (count($disabled_category) > 0) {
            $this->db->where_not_in('a.category', $disabled_category);
        }

        $compiled_select = $this->db->get_compiled_select();

        $this->db->select('a.name AS category, d.asseturl')
            ->from('mscategory a')
            ->join('msproduct b', 'b.category = a.name AND b.userid = a.userid')
            ->join('categoryimage c', 'c.categoryname = b.category AND c.userid = b.userid')
            ->join('msicons d', 'd.id = c.assetid')
            ->where(array(
                'a.userid' => $this->merchant->id,
                "a.servicesubtype" => 'PRABAYAR',
            ))
            ->group_by('a.name')
            ->order_by('a.name', 'ASC');

        $where = array(
            'b.userid' => $this->merchant->id,
            'b.category_apikey' => 'PPOB',
            'b.subcategory_apikey' => 'PRABAYAR',
            'b.category !=' => null
        );

        if ($this->merchant->multivendor != 1) {
            $where["(b.vendor = '$current_vendor' OR b.vendor IS NULL) ="] = true;
            $where['b.vendorid'] = null;
        } else {
            $where["((b.vendorid IS NOT NULL AND b.vendorenabled = 1) OR b.vendor IS NULL) ="] = true;
        }

        $this->db->where($where);

        if (count($disabled_category)) {
            $this->db->where_not_in('a.name', $disabled_category);
        }

        $compiled_select2 = $this->db->get_compiled_select();

        $query = $this->db->query("SELECT * FROM (($compiled_select) UNION ($compiled_select2)) a GROUP BY a.category ORDER BY a.category ASC")->result();

        $data = array();
        $data['category'] = $query;

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => viewTemplate($this->merchant->id, "landing/ppob/category", $data, true)
        ));
    }

    public function ppob_subcategory()
    {
        if (getCurrentThemeConfiguration($this->merchant->id) != 'Fin-App' && getCurrentThemeConfiguration($this->merchant->id) != 'Sobat-Serverppob') {
            return show_404();
        }

        $category = getPost('category');
        $subcategory = getPost('subcategory');
        $searchfilter = strtolower(getPost('searchfilter') ?? '');

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $disabledbrand = $this->disabledbrand->result(array(
            'userid' => $this->merchant->id,
        ));

        $disabled_brand = array();
        foreach ($disabledbrand as $key => $value) {
            $disabled_brand[] = $value->brandname;
        }

        $where = array(
            'a.userid' => $this->merchant->id,
            "a.subcategory_apikey" => 'PRABAYAR',
            'a.status' => 1
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where["(a.vendor = '$vendor' OR a.vendor IS NULL) ="] = true;
            $where['a.vendorid'] = null;
        } else {
            $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
        }

        if ($category != null) {
            $where['a.category'] = $category;
        }

        $categoryproduct = $this->db->select('a.brand, c.asseturl')
            ->from('msproduct a')
            ->join('brandimage b', 'b.brandname = a.brand AND b.userid = a.userid AND a.category = b.categoryname')
            ->join('msicons c', 'c.id = b.assetid')
            ->where($where)
            ->group_by('a.brand')
            ->order_by('a.brand', 'ASC');

        if (count($disabled_category) > 0) {
            $this->db->where_not_in('a.category', $disabled_category);
        }

        if (count($disabled_brand) > 0) {
            $this->db->where_not_in('a.brand', $disabled_brand);
        }

        if ($subcategory != null) {
            $this->db->where('a.type', $subcategory);
        }

        $categoryproduct = $this->db->get_compiled_select();

        $getcategory = $this->db->select('a.name AS brand, c.asseturl')
            ->from('msbrand a')
            ->join('brandimage b', 'b.brandname = a.name AND b.userid = a.userid AND a.category = b.categoryname')
            ->join('msicons c', 'c.id = b.assetid')
            ->join('msproduct d', 'd.userid = a.userid AND d.brand = a.name AND d.category = a.category')
            ->where(array(
                'a.userid' => $this->merchant->id,
                'a.category' => $category
            ))
            ->group_by('a.name, c.asseturl')
            ->order_by('a.name', 'ASC');

        if (count($disabled_brand) > 0) {
            $this->db->where_not_in('name', $disabled_brand);
        }

        $getcategory = $this->db->get_compiled_select();

        if ($searchfilter == null) {
            $get = $this->db->query("SELECT * FROM (($categoryproduct) UNION ($getcategory)) a GROUP BY a.brand ORDER BY a.brand ASC");
        } else {
            $get = $this->db->query("SELECT * FROM (($categoryproduct) UNION ($getcategory)) a WHERE LOWER(a.brand) LIKE '%$searchfilter%' GROUP BY a.brand ORDER BY a.brand ASC");
        }

        $data = array();
        $data['subcategory'] = $get->result();
        $data['category'] = $category;

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => viewTemplate($this->merchant->id, "landing/ppob/subcategory", $data, true),
            'COUNTCATEGORY' => $get->num_rows()
        ));
    }

    public function modalsubprovider()
    {
        if (getCurrentThemeConfiguration($this->merchant->id) != 'Fin-App' && getCurrentThemeConfiguration($this->merchant->id) != 'Sobat-Serverppob') {
            return show_404();
        }

        $data = stringEncryption('decrypt', getPost('data'));
        $data = json_decode($data);
        $category = $data->category ?? null;
        $brand = $data->brand ?? null;
        $asseturl = $data->asseturl ?? null;

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB',
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.subcategory_apikey' => 'PRABAYAR',
            'a.status' => 1,
            'a.category' => $category,
            'a.brand' => $brand,
            'a.type !=' => null
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where['a.vendor'] = $vendor;
            $where['a.vendorid'] = null;
        } else {
            $where['a.vendorid !='] = null;
            $where['a.vendorenabled'] = 1;
        }

        $subcategoryproduct = $this->db->select('type')
            ->from('msproduct a')
            ->where($where)
            ->group_by('type')
            ->order_by('type', 'ASC');

        if (count($disabled_category) > 0) {
            $this->db->where_not_in('category', $disabled_category);
        }

        $subcategoryproduct = $this->db->get()->result();

        $data = array();
        $data['subcategoryproduct'] = $subcategoryproduct;
        $data['category'] = $category;
        $data['brand'] = $brand;
        $data['asseturl'] = $asseturl;

        $has_subcategory = count($subcategoryproduct) > 0 ? 1 : 0;

        if ($has_subcategory > 0) {
            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => viewTemplate($this->merchant->id, "landing/ppob/modalsubprovider", $data, true)
            ));
        } else {
            return JSONResponse(array(
                'RESULT' => 'OK',
                'REDIRECT' => base_url('landing/ppob/product/' . $category . '/' . $brand . '?key=' . stringEncryption('encrypt', json_encode(array(
                    'category' => $category,
                    'brand' => $brand,
                    'subcategory' => null
                ))) . '&userid=' . $this->userid)
            ));
        }
    }

    public function ppob_product($id, $id2)
    {
        if (getCurrentThemeConfiguration($this->merchant->id) != 'Fin-App' && getCurrentThemeConfiguration($this->merchant->id) != 'Sobat-Serverppob') {
            return show_404();
        }

        $key = getGet('key');
        $key = stringEncryption('decrypt', $key);

        if ($key == null) {
            return show_404();
        }

        $key = json_decode($key, true);

        if (!isset($key['category']) || !isset($key['brand'])) {
            return show_404();
        }

        $category = $key['category'];
        $subcategory = $key['subcategory'] ?? null;
        $brand = $key['brand'];

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $disabledbrand = $this->disabledbrand->result(array(
            'userid' => $this->merchant->id,
        ));

        $disabled_brand = array();
        foreach ($disabledbrand as $key => $value) {
            $disabled_brand[] = $value->brandname;
        }

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.subcategory_apikey' => 'PRABAYAR',
            'a.category' => $category,
            'a.brand' => $brand
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where["(a.vendor = '$vendor' OR a.vendor IS NULL) ="] = true;
            $where['a.vendorid'] = null;
        } else {
            $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
        }

        $this->msproduct->order_by('a.price', 'ASC')
            ->where_not_in('category', $disabled_category)
            ->where_not_in('brand', $disabled_brand);

        if ($subcategory != null) {
            $this->msproduct->where('a.type', $subcategory);
        }

        $get = $this->msproduct->select('a.*, c.asseturl')
            ->join('brandimage b', 'b.brandname = a.brand AND b.userid = a.userid AND b.categoryname = a.category', 'LEFT')
            ->join('msicons c', 'c.id = b.assetid', 'LEFT')
            ->where($where)
            ->group_by('a.id')
            ->get();

        if ($get->num_rows() == 0) {
            return show_404();
        }

        $currentuser = getCurrentUser();

        if (($currentuser->roleid ?? null) == null) {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'isdefault' => '1'
            ))->row();
        } else {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'id' => $currentuser->roleid
            ))->row();
        }

        if ($getrole != null) {
            if ($getrole->discounttype == 'Simple') {
                $getrolediscount = $this->msrolediscount->get(array(
                    'userid' => $this->merchant->id,
                    'roleid' => $getrole->id,
                ))->row();

                $discount = $getrolediscount->trxdiscount ?? 0;
            } else {
                $discount = $this->msrolediscountadv->get(array(
                    'createdby' => $this->merchant->id,
                    'roleid' => $getrole->id,
                    'servicetype' => 'Prabayar'
                ))->result();
            }
        } else {
            $getrole = new stdClass();
            $getrole->discounttype = 'Simple';
            $discount = 0;
        }

        $data = array();
        $data['title'] = 'Product';
        $data['content'] = 'landing/ppob/product';
        $data['product'] = $get->result();
        $data['row'] = $get->row();
        $data['discount'] = $discount;
        $data['getrole'] = $getrole;
        $data['back'] = base_url('?userid=' . $this->userid);

        $paymentgateway = $this->mspaymentgateway->result(array(
            'userid' => $this->merchant->id,
            "(isdisabled IS NULL OR isdisabled = 0) =" => true
        ));

        $addons_select = array();
        foreach ($paymentgateway as $key => $value) {
            if ($value->type == 'Payment Gateway') {
                $feepaymentgateway = $this->feepaymentgateway->result(array(
                    'paymentgatewayid' => $value->id,
                    'vendor' => $value->vendor,
                ));

                $fee = array();
                foreach ($feepaymentgateway as $k => $v) {
                    $fee[$v->paymentname] = array(
                        'fee' => round($v->fee),
                        'type' => $v->feetype,
                        'paymentimage' => $v->paymentimage ?? null
                    );
                }

                if ($value->vendor == 'Midtrans') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->enabled_payments)) {
                        foreach ($addons->enabled_payments as $k => $v) {
                            if (isset(getMidtransPayments()[$v])) {
                                $select = array(
                                    'id' => stringEncryption('encrypt', $v),
                                    'data' => getMidtransPayments()[$v],
                                    'nominalfee' => $fee[$v]['fee'] ?? 0,
                                    'feetype' => $fee[$v]['type'] ?? 'Nominal',
                                    'paymentimage' => $fee[$v]['paymentimage'] ?? null,
                                    'minnominal' => $value->minnominal,
                                    'maxnominal' => $value->maxnominal,
                                );
                                array_push($addons_select, $select);
                            }
                        }
                    }
                } elseif ($value->vendor == 'Tripay') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            if (isset(getTripayPayments()[$v])) {
                                $select = array(
                                    'id' => stringEncryption('encrypt', $v),
                                    'data' => $addons->alias_payments[$k] ?? getTripayPayments()[$v],
                                    'nominalfee' => $fee[$v]['fee'] ?? 0,
                                    'feetype' => $fee[$v]['type'] ?? 'Nominal',
                                    'paymentimage' => $fee[$v]['paymentimage'] ?? null,
                                    'minnominal' => $value->minnominal,
                                    'maxnominal' => $value->maxnominal,
                                );
                                array_push($addons_select, $select);
                            }
                        }
                    }
                } elseif ($value->vendor == 'iPaymu') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            if (isset(getiPaymuPayments()[$v])) {
                                $select = array(
                                    'id' => stringEncryption('encrypt', $v),
                                    'data' => $addons->alias_payments[$k] ?? getiPaymuPayments()[$v],
                                    'nominalfee' => $fee[$v]['fee'] ?? 0,
                                    'feetype' => $fee[$v]['type'] ?? 'Nominal',
                                    'paymentimage' => $fee[$v]['paymentimage'] ?? null,
                                    'minnominal' => $value->minnominal,
                                    'maxnominal' => $value->maxnominal,
                                );
                                array_push($addons_select, $select);
                            }
                        }
                    }
                } elseif ($value->vendor == 'Duitku') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments) && isset($addons->channel_payments_name)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            $select = array(
                                'id' => stringEncryption('encrypt', $v),
                                'data' => $addons->alias_payments[$k] ?? $addons->channel_payments_name[$k],
                                'nominalfee' => $fee[$v]['fee'] ?? 0,
                                'feetype' => $fee[$v]['type'] ?? 'Nominal',
                                'paymentimage' => $fee[$v]['paymentimage'] ?? null,
                                'minnominal' => $value->minnominal,
                                'maxnominal' => $value->maxnominal,
                            );
                            array_push($addons_select, $select);
                        }
                    }
                } elseif ($value->vendor == 'Okeconnect') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments) && isset($addons->channel_payments_name)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            $select = array(
                                'id' => stringEncryption('encrypt', $v),
                                'data' => $addons->alias_payments[$k] ?? getOkeconnectPayments()[$v],
                                'nominalfee' => $fee[$v]['fee'] ?? 0,
                                'feetype' => $fee[$v]['type'] ?? 'Nominal',
                                'paymentimage' => $fee[$v]['paymentimage'] ?? null,
                                'minnominal' => $value->minnominal,
                                'maxnominal' => $value->maxnominal,
                            );
                            array_push($addons_select, $select);
                        }
                    }
                } else if ($value->vendor == 'PayDisini') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments) && isset($addons->channel_payments_name)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            $select = array(
                                'id' => stringEncryption('encrypt', json_encode(array('id' => $v, 'type' => $addons->channel_payments_type[$k]))),
                                'data' => $addons->alias_payments[$k] ?? $addons->channel_payments_name[$k],
                                'nominalfee' => $fee[$v]['fee'] ?? 0,
                                'feetype' => $fee[$v]['type'] ?? 'Nominal',
                                'paymentimage' => $fee[$v]['paymentimage'] ?? null,
                                'minnominal' => $value->minnominal,
                                'maxnominal' => $value->maxnominal,
                            );
                            array_push($addons_select, $select);
                        }
                    }
                }
            } else {
                $select = array(
                    'id' => stringEncryption('encrypt', $value->id),
                    'data' => $value->type,
                    'nominalfee' => $value->nominalfee ?? 0,
                    'feetype' => $value->feetype ?? 'Nominal',
                    'paymentimage' => $value->paymentimage ?? null,
                    'minnominal' => $value->minnominal,
                    'maxnominal' => $value->maxnominal,
                );
                array_push($addons_select, $select);
            }
        }

        $notificationhandler = $this->userbuynotificationhandler->select('a.*, b.packagename')
            ->join('msnotificationhandlerpackage b', 'b.id = a.notificationid')
            ->result(array(
                'a.userid' => $this->merchant->id,
                'b.isactive' => 1,
                "(isdisabled IS NULL OR isdisabled = 0) =" => true
            ));

        foreach ($notificationhandler as $key => $value) {
            $keys = stringEncryption('encrypt', json_encode(array(
                'id' => $value->id,
                'type' => 'Notification Handler',
            )));

            $select = array(
                'id' => $keys,
                'data' => $value->packagename,
                'minnominal' => $value->minnominal,
                'maxnominal' => $value->maxnominal,
                'nominalfee' => $value->isfee == 1 ? $value->nominalfee : 0,
                'feetype' => $value->feetype,
                'paymentimage' => $value->paymentimage ?? null,
            );
            array_push($addons_select, $select);
        }

        $payment = $this->mspaymentmethod->result(array(
            'userid' => $this->merchant->id,
            "(isdisabled = 0 OR isdisabled IS NULL) =" => true
        ));

        asort($addons_select);
        $data['paymentotomatis'] = $addons_select;
        $data['paymentmanual'] =  $payment;

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function confirm_order_ppob()
    {
        try {
            if (getCurrentThemeConfiguration($this->merchant->id) != 'Fin-App' && getCurrentThemeConfiguration($this->merchant->id) != 'Sobat-Serverppob') {
                return show_404();
            }

            $phoneUtil = PhoneNumberUtil::getInstance();

            $product = getPost('product');
            $target = getPost('target');
            $zoneid = getPost('zoneid');
            $pin = getPost('pin');
            $paymenttype = getPost('paymentmethodtype');
            $payment = getPost('paymentmethod');
            $phonenumber = getPost('phonenumber');
            $email = getPost('email');

            if ($product == null) {
                throw new Exception('Produk wajib diisi');
            } else if (!is_numeric(stringEncryption('decrypt', $product))) {
                throw new Exception('Produk tidak ditemukan');
            } else if ($target == null) {
                throw new Exception('Target wajib diisi');
            } else if ($phonenumber == null) {
                throw new Exception('Nomor Telepon wajib diisi');
            } else if ($paymenttype == null) {
                throw new Exception('Metode Pembayaran wajib diisi');
            } else if ($payment == null) {
                throw new Exception('Pembayaran wajib diisi');
            } else if ($email == null) {
                throw new Exception('Email wajib diisi');
            } else if (!filter_var($email)) {
                throw new Exception('Format Email tidak valid');
            } else {
                try {
                    $idNumberProto = $phoneUtil->parse($phonenumber, 'ID');
                    $isValid = $phoneUtil->isValidNumber($idNumberProto);

                    if (!$isValid) {
                        throw new Exception('Format Nomor Telepon tidak valid');
                    }

                    $phonenumber = $phoneUtil->format($idNumberProto, PhoneNumberFormat::E164);
                    $phonenumber = substr($phonenumber, 1);
                } catch (NumberParseException $ex) {
                    throw new Exception('Format Nomor Telepon tidak valid');
                }
            }

            $payment = stringEncryption('decrypt', $payment);
            $product = stringEncryption('decrypt', $product);

            $currentPhone = $phonenumber;

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $disabledbrand = $this->disabledbrand->result(array(
                'userid' => $this->merchant->id,
            ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            $where = array(
                'a.id' => $product,
                'a.userid' => $this->merchant->id,
                'a.subcategory_apikey' => 'PRABAYAR'
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('PPOB', $this->merchant->id);
                $where["(a.vendor = '$vendor' OR a.vendor IS NULL) ="] = true;
                $where['a.vendorid'] = null;
            } else {
                $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
            }

            $getproduct = $this->msproduct->select('c.asseturl')
                ->join('brandimage b', 'b.brandname = a.brand AND b.userid = a.userid AND b.categoryname = a.category', 'LEFT')
                ->join('msicons c', 'c.id = b.assetid', 'LEFT')
                ->where($where)
                ->where_not_in('category', $disabled_category)
                ->where_not_in('brand', $disabled_brand)
                ->group_by('a.id')
                ->get();

            if ($getproduct->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $productRow = $getproduct->row();

            if ($productRow->status != 1) {
                throw new Exception('Produk tidak aktif');
            }

            if ($productRow->isstock == 1) {
                $stock = $productRow->stock;

                if ($stock == 0) {
                    throw new Exception('Produk sedang kosong');
                }
            }

            if ($productRow->isdatabase == 1 && $productRow->databasecategory == 'Stock Product') {
                $databaseid = $productRow->databaseid;

                $stockproduct = $this->msstockproduct->get(array(
                    'id' => $databaseid
                ))->row();

                if ($stockproduct == null) {
                    throw new Exception('Produk tidak tersedia');
                } else {
                    $available_stock = $this->msstockproductdetail->total(array(
                        'stockproductid' => $databaseid,
                        'status' => 'Tersedia'
                    ));

                    if ($available_stock == 0) {
                        throw new Exception('Produk sedang kosong');
                    }
                }
            }

            if (isLogin()) {
                if ($pin == null) {
                    throw new Exception('PIN Transaksi wajib diisi');
                }

                $currentuser = getCurrentUser();

                if (stringEncryption('encrypt', $pin) != $currentuser->pin) {
                    throw new Exception('PIN Transaksi yang anda masukkan salah');
                }

                if ($currentuser->roleid == null) {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'isdefault' => '1'
                    ))->row();
                } else {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'id' => $currentuser->roleid
                    ))->row();
                }

                if ($getrole != null) {
                    if ($getrole->discounttype == 'Simple') {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->get(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            'servicetype' => 'Prabayar'
                        ))->result();
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = 'Simple';
                    $discount = 0;
                }

                $getpending = $this->trorder->total(array(
                    'userid' => getCurrentIdUser(),
                    'serviceid' => $productRow->id,
                    'target' => $target,
                    'status' => 'pending'
                ));

                if ($getpending > 0) {
                    throw new Exception('Transaksi sedang diproses');
                }
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();

                if ($getrole != null) {
                    if ($getrole->discounttype == 'Simple') {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->get(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            'servicetype' => 'Prabayar'
                        ))->result();
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = 'Simple';
                    $discount = 0;
                }

                $getpending = $this->trorder->total(array(
                    'clientip' => get_client_ip(),
                    'serviceid' => $productRow->id,
                    'target' => $target,
                    'status' => 'pending'
                ));

                if ($getpending > 0) {
                    throw new Exception('Transaksi sedang diproses');
                }
            }

            $fixproductprice = $productRow->price;

            if ($getrole->discounttype == 'Simple') {
                $fixproductprice = $productRow->price - $discount;
            } else {
                $found = false;

                foreach ($discount as $val) {
                    if ($found) continue;

                    if ($val->startrange <= $productRow->price && $val->endrange >= $productRow->price && strtoupper($val->servicetype) == 'PRABAYAR') {
                        if ($val->discounttype == 'Persentase') {
                            $fixproductprice = round($productRow->price - ($productRow->price * $val->nominal / 100));

                            $found = true;
                        } else {
                            $fixproductprice = $productRow->price - $val->nominal;

                            $found = true;
                        }
                    }
                }
            }

            if ($productRow->vendor != null) {
                if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand)) && $productRow->vendor == 'VIPayment') {
                    if ($zoneid == null) {
                        throw new Exception('Zone ID wajib diisi');
                    }
                }
            }

            $feeamount = 0;

            if ($paymenttype == 'Saldo' && isLogin()) {
                if (getCurrentBalance() < $fixproductprice) {
                    throw new Exception('Saldo anda tidak mencukupi');
                }

                $paymentimage = null;
            } else if ($paymenttype == 'Manual') {
                $get = $this->mspaymentmethod->get(array(
                    'userid' => $this->merchant->id,
                    'id' => $payment,
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Pembayaran tidak ditemukan');
                }

                $row = $get->row();

                if ($row->isdisabled == 1) {
                    throw new Exception('Pembayaran yang anda pilih tidak aktif');
                } elseif ($row->minnominal > $fixproductprice) {
                    throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($row->minnominal));
                } else if ($row->maxnominal != 0 && $row->maxnominal < $fixproductprice) {
                    throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($row->maxnominal));
                }

                if ($row->feetype != null) {
                    $feeamount = 0;
                    if ($row->feetype == 'Persentase') {
                        $feeamount = $fixproductprice * (((int) $row->nominalfee) / 100);
                    } else if ($row->feetype == 'Nominal') {
                        $feeamount = $row->nominalfee;
                    }
                }

                $paymentimage = $row->paymentimage;
            } else if ($paymenttype == 'Otomatis') {
                if (!is_numeric($payment)) {
                    $json = json_decode($payment);

                    if (!is_object($json) && !isset($json->type)) {
                        $paymentgateway = $this->mspaymentgateway->get(array(
                            'userid' => $this->merchant->id,
                            'type' => 'Payment Gateway',
                            "(isdisabled IS NULL OR isdisabled = 0) =" => true
                        ))->row();

                        if ($paymentgateway == null) {
                            throw new Exception('Pembayaran tidak ditemukan');
                        } else if ($paymentgateway->isdisabled == 1) {
                            throw new Exception('Pembayaran yang anda pilih tidak aktif');
                        }

                        if ($paymentgateway->minnominal > $fixproductprice) {
                            throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->minnominal));
                        } else if ($paymentgateway->maxnominal != 0 && $paymentgateway->maxnominal < $fixproductprice) {
                            throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->maxnominal));
                        }

                        if ($paymentgateway != null && $paymentgateway->vendor == 'Midtrans' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->enabled_payments)) {
                                if (in_array($payment, $addons->enabled_payments) && isset(getMidtransPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $fixproductprice * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }
                                    }

                                    $paymentimage = $fee->paymentimage ?? null;
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Tripay' && $paymentgateway->addons != null) {
                            if ($payment == 'OVO') {
                                if ($currentPhone == null) {
                                    throw new Exception('Nomor Handphone OVO tidak boleh kosong');
                                } else if (!is_numeric($currentPhone)) {
                                    throw new Exception('Nomor Handphone OVO tidak valid');
                                }
                            }

                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getTripayPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $fixproductprice * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }
                                    }

                                    $paymentimage = $fee->paymentimage ?? null;
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'iPaymu' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getIpaymuPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $fixproductprice * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }
                                    }

                                    $paymentimage = $fee->paymentimage ?? null;
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Duitku' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments) && isset($addons->channel_payments_name) && isset($detail->apikey) && isset($detail->merchantcode)) {
                                if (in_array($payment, $addons->channel_payments)) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $fixproductprice * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }
                                    }

                                    $paymentimage = $fee->paymentimage ?? null;
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Okeconnect' && $paymentgateway->addons != null) {

                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getOkeconnectPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $fixproductprice * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }
                                    }

                                    $paymentimage = $fee->paymentimage ?? null;
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else {
                            throw new Exception('Pembayaran tidak ditemukan');
                        }
                    } else {
                        if (isset($json->type) && $json->type == 'Notification Handler') {
                            $userbuynotificationhandler = $this->userbuynotificationhandler->select('a.*, b.packagename')
                                ->join('msnotificationhandlerpackage b', 'b.id = a.notificationid')
                                ->get(array(
                                    'a.userid' => $this->merchant->id,
                                    'a.id' => $json->id,
                                    'b.isactive' => 1,
                                    "(a.isdisabled IS NULL OR a.isdisabled = '0') =" => true,
                                ));

                            if ($userbuynotificationhandler->num_rows() == 0) {
                                throw new Exception('Pembayaran tidak ditemukan');
                            }

                            $rowuserbuynotificationhandler = $userbuynotificationhandler->row();

                            if ($rowuserbuynotificationhandler->isdisabled == 1) {
                                throw new Exception('Pembayaran yang anda pilih tidak aktif');
                            } else if ($rowuserbuynotificationhandler->minnominal > $fixproductprice) {
                                throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($rowuserbuynotificationhandler->minnominal));
                            } else if ($rowuserbuynotificationhandler->maxnominal != 0 && $rowuserbuynotificationhandler->maxnominal < $fixproductprice) {
                                throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($rowuserbuynotificationhandler->maxnominal));
                            }

                            if ($rowuserbuynotificationhandler->isfee == 1) {
                                $feeamount = 0;
                                if ($rowuserbuynotificationhandler->feetype == 'Persentase') {
                                    $feeamount = $fixproductprice * $rowuserbuynotificationhandler->nominalfee / 100;
                                } else if ($rowuserbuynotificationhandler->feetype == 'Nominal') {
                                    $feeamount = $rowuserbuynotificationhandler->nominalfee;
                                }
                            }

                            $paymentimage = $rowuserbuynotificationhandler->paymentimage;
                        } else {
                            $paymentgateway = $this->mspaymentgateway->get(array(
                                'userid' => $this->merchant->id,
                                'type' => 'Payment Gateway',
                                "(isdisabled IS NULL OR isdisabled = 0) =" => true
                            ))->row();

                            if ($paymentgateway == null) {
                                throw new Exception('Pembayaran tidak ditemukan');
                            } else if ($paymentgateway->isdisabled == 1) {
                                throw new Exception('Pembayaran yang anda pilih tidak aktif');
                            }

                            if ($paymentgateway->minnominal > $fixproductprice) {
                                throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->minnominal));
                            } else if ($paymentgateway->maxnominal != 0 && $paymentgateway->maxnominal < $fixproductprice) {
                                throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->maxnominal));
                            }

                            if ($paymentgateway->vendor == 'PayDisini' && $paymentgateway->addons != null && isset($json->id)) {
                                $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                                $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                                $paydisini = new PayDisini($detail->apikey);
                                $paymentchannel = $paydisini->paymentChannel();

                                if (isset($paymentchannel->success) && $paymentchannel->success == true) {
                                    $paymentchannel = $paymentchannel->data;

                                    if (isset($addons->channel_payments)) {
                                        if (in_array($json->id, $addons->channel_payments) && in_array($json->id, array_column($paymentchannel, 'id'))) {
                                            $fee = $this->feepaymentgateway->row(array(
                                                'paymentgatewayid' => $paymentgateway->id,
                                                'paymentname' => $json->id,
                                                "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                            ));

                                            if ($fee != null) {
                                                $feeamount = 0;
                                                if ($fee->feetype == 'Persentase') {
                                                    $feeamount = $fixproductprice * $fee->fee / 100;
                                                } else if ($fee->feetype == 'Nominal') {
                                                    $feeamount = $fee->fee;
                                                }
                                            }

                                            $paymentimage = $fee->paymentimage ?? null;
                                        } else {
                                            throw new Exception('Pembayaran tidak ditemukan');
                                        }
                                    } else {
                                        throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Pembayaran tidak ditemukan');
                            }
                        }
                    }
                } else {
                    $paymentgateway = $this->mspaymentgateway->get(array(
                        'id' => $payment,
                        'userid' => $this->merchant->id,
                        "(isdisabled IS NULL OR isdisabled = 0) =" => true
                    ));

                    if ($paymentgateway->num_rows() == 0) {
                        throw new Exception('Pembayaran tidak ditemukan');
                    }

                    $rowpaymentgateway = $paymentgateway->row();

                    if ($rowpaymentgateway->isdisabled == 1) {
                        throw new Exception('Pembayaran yang anda pilih tidak aktif');
                    } else if ($rowpaymentgateway->minnominal > $fixproductprice) {
                        throw new Exception('Minimal Topup Rp' . IDR($rowpaymentgateway->minnominal));
                    } else if ($rowpaymentgateway->maxnominal != 0 && $rowpaymentgateway->maxnominal < $fixproductprice) {
                        throw new Exception('Maksimal Topup Rp' . IDR($rowpaymentgateway->maxnominal));
                    }

                    if ($rowpaymentgateway->feetype != null) {
                        $feeamount = 0;

                        if ($rowpaymentgateway->feetype == 'Persentase') {
                            $feeamount = $fixproductprice * $rowpaymentgateway->nominalfee / 100;
                        } else if ($rowpaymentgateway->feetype == 'Nominal') {
                            $feeamount = $rowpaymentgateway->nominalfee;
                        }
                    }

                    $paymentimage = $rowpaymentgateway->paymentimage;
                }
            } else {
                throw new Exception('Metode Pembayaran tidak ditemukan');
            }

            $data = array();
            $data['product'] = $productRow;
            $data['target'] = $target;
            $data['zoneid'] = $zoneid;
            $data['pin'] = $pin;
            $data['paymenttype'] = $paymenttype;
            $data['payment'] = stringEncryption('encrypt', $payment);
            $data['phonenumber'] = $phonenumber;
            $data['email'] = $email;
            $data['fee'] = $feeamount;
            $data['price'] = $fixproductprice;
            $data['paymentimage'] = $paymentimage;

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => viewTemplate($this->merchant->id, "landing/ppob/confirm_order", $data, true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_order_ppob()
    {
        try {
            $this->db->trans_begin();

            if (getCurrentThemeConfiguration($this->merchant->id) != 'Fin-App' && getCurrentThemeConfiguration($this->merchant->id) != 'Sobat-Serverppob') {
                $this->db->trans_rollback();

                return show_404();
            }

            $phoneUtil = PhoneNumberUtil::getInstance();

            $product = getPost('product');
            $target = getPost('target');
            $zoneid = getPost('zoneid');
            $pin = getPost('pin');
            $paymenttype = getPost('paymenttype');
            $payment = getPost('paymentmethod');
            $phonenumber = getPost('phonenumber');
            $email = getPost('email');

            if ($product == null) {
                throw new Exception('Produk wajib diisi');
            } else if (!is_numeric(stringEncryption('decrypt', $product))) {
                throw new Exception('Produk tidak ditemukan');
            } else if ($target == null) {
                throw new Exception('Target wajib diisi');
            } else if ($phonenumber == null) {
                throw new Exception('Nomor Telepon wajib diisi');
            } else if ($paymenttype == null) {
                throw new Exception('Metode Pembayaran wajib diisi');
            } else if ($payment == null) {
                throw new Exception('Pembayaran wajib diisi');
            } else if ($email == null) {
                throw new Exception('Email wajib diisi');
            } else if (!filter_var($email)) {
                throw new Exception('Format Email tidak valid');
            } else {
                try {
                    $idNumberProto = $phoneUtil->parse($phonenumber, 'ID');
                    $isValid = $phoneUtil->isValidNumber($idNumberProto);

                    if (!$isValid) {
                        throw new Exception('Format Nomor Telepon tidak valid');
                    }

                    $phonenumber = $phoneUtil->format($idNumberProto, PhoneNumberFormat::E164);
                    $phonenumber = substr($phonenumber, 1);
                } catch (NumberParseException $ex) {
                    throw new Exception('Format Nomor Telepon tidak valid');
                }
            }

            $payment = stringEncryption('decrypt', $payment);
            $product = stringEncryption('decrypt', $product);

            $currentName = $target;
            $currentPhone = $phonenumber;
            $CurrentEmail = $email ?? null;

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $disabledbrand = $this->disabledbrand->result(array(
                'userid' => $this->merchant->id,
            ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            $where = array(
                'id' => $product,
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'PRABAYAR'
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('PPOB', $this->merchant->id);
                $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                $where['vendorid'] = null;
            } else {
                $where["((vendorid IS NOT NULL AND a.vendorenabled = 1) OR vendor IS NULL) ="] = true;
            }

            $getproduct = $this->msproduct->where_not_in('category', $disabled_category)
                ->where_not_in('brand', $disabled_brand)
                ->get($where);

            if ($getproduct->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $productRow = $getproduct->row();

            if ($productRow->status != 1) {
                throw new Exception('Produk sedang gangguan');
            }

            if ($productRow->isstock == 1) {
                $stock = $productRow->stock;

                if ($stock == 0) {
                    throw new Exception('Produk sedang kosong');
                }
            }

            if ($productRow->isdatabase == 1 && $productRow->databasecategory == 'Stock Product') {
                $databaseid = $productRow->databaseid;

                $stockproduct = $this->msstockproduct->get(array(
                    'id' => $databaseid
                ))->row();

                if ($stockproduct == null) {
                    throw new Exception('Produk tidak tersedia');
                } else {
                    $available_stock = $this->msstockproductdetail->total(array(
                        'stockproductid' => $databaseid,
                        'status' => 'Tersedia'
                    ));

                    if ($available_stock == 0) {
                        throw new Exception('Produk sedang kosong');
                    }
                }
            }

            $insert = array();
            $insert['clientcode'] = generateTransactionNumber('PRABAYAR');
            $insert['userid'] = getCurrentIdUser();

            if (isLogin()) {
                if ($pin == null) {
                    throw new Exception('PIN Transaksi wajib diisi');
                }

                $currentuser = getCurrentUser(null, true);

                $currentName = $currentuser->name;
                $CurrentEmail = $currentuser->email;

                if (stringEncryption('encrypt', $pin) != $currentuser->pin) {
                    throw new Exception('PIN Transaksi yang anda masukkan salah');
                }

                if ($currentuser->roleid == null) {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'isdefault' => '1'
                    ))->row();
                } else {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'id' => $currentuser->roleid
                    ))->row();
                }

                if ($getrole != null) {
                    if ($getrole->discounttype == 'Simple') {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->get(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            'servicetype' => 'Prabayar'
                        ))->result();
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = 'Simple';
                    $discount = 0;
                }

                $getpending = $this->trorder->total(array(
                    'userid' => getCurrentIdUser(),
                    'serviceid' => $productRow->id,
                    'target' => $target,
                    'status' => 'pending'
                ));

                if ($getpending > 0) {
                    throw new Exception('Terdapat transaksi yang sedang diproses');
                }
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();

                if ($getrole != null) {
                    if ($getrole->discounttype == 'Simple') {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->get(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            'servicetype' => 'Prabayar'
                        ))->result();
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = 'Simple';
                    $discount = 0;
                }

                $insert['clientip'] = get_client_ip();

                $getpending = $this->trorder->total(array(
                    'clientip' => get_client_ip(),
                    'serviceid' => $productRow->id,
                    'target' => $target,
                    'status' => 'pending'
                ));

                if ($getpending > 0) {
                    throw new Exception('Terdapat transaksi yang sedang diproses');
                }
            }

            $fixproductprice = $productRow->price;

            if ($getrole->discounttype == 'Simple') {
                $fixproductprice = $productRow->price - $discount;
            } else {
                $found = false;

                foreach ($discount as $val) {
                    if ($found) continue;

                    if ($val->startrange <= $productRow->price && $val->endrange >= $productRow->price && strtoupper($val->servicetype) == 'PRABAYAR') {
                        if ($val->discounttype == 'Persentase') {
                            $fixproductprice = round($productRow->price - ($productRow->price * $val->nominal / 100));

                            $found = true;
                        } else {
                            $fixproductprice = $productRow->price - $val->nominal;

                            $found = true;
                        }
                    }
                }
            }

            $fixproductprice_forprofit = $fixproductprice;

            $queuetransaction = false;
            if ($productRow->vendor != null) {
                if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand)) && $productRow->vendor == 'VIPayment') {
                    if ($zoneid == null) {
                        throw new Exception('Zone ID wajib diisi');
                    }
                }
            }

            if ($paymenttype == 'Saldo' && isLogin()) {
                if ($currentuser->balance < $fixproductprice) {
                    throw new Exception('Saldo anda tidak mencukupi');
                }

                if ($this->merchant->multivendor != 1) {
                    $apikeys = getCurrentAPIKeys('PPOB', $this->merchant->id);
                    if ($apikeys->balance < $productRow->vendorprice) {
                        $queuetransaction = true;
                    }
                } else {
                    if (getCurrentBalanceVendor($this->merchant->id, $productRow->vendor) < $productRow->vendorprice) {
                        $queuetransaction = true;
                    }
                }

                $insert['status_payment'] = 'sukses';
                $insert['price'] = $fixproductprice;
                $insert['currentsaldo'] = $currentuser->balance;
            } else if ($paymenttype == 'Manual') {
                $get = $this->mspaymentmethod->get(array(
                    'userid' => $this->merchant->id,
                    'id' => $payment,
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Pembayaran tidak ditemukan');
                }

                $row = $get->row();

                if ($row->isdisabled == 1) {
                    throw new Exception('Pembayaran yang anda pilih tidak aktif');
                } elseif ($row->minnominal > $fixproductprice) {
                    throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($row->minnominal));
                } else if ($row->maxnominal != 0 && $row->maxnominal < $fixproductprice) {
                    throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($row->maxnominal));
                } else if ($row->isunique == 1) {
                    if ($this->merchant->uniquenominal_start != null && $this->merchant->uniquenominal_end != null && $this->merchant->uniquenominal_start != 0 && $this->merchant->uniquenominal_end != 0) {
                        $unique = rand($this->merchant->uniquenominal_start, $this->merchant->uniquenominal_end);
                    } else {
                        $unique = rand(100, 999);
                    }

                    $fixproductprice = $fixproductprice + $unique;

                    if ($row->uniqueadmin == 1) {
                        $insert['uniqueadmin'] = $unique;
                    }
                }

                $insert['payment'] = $row->paymentmethod;
                $insert['paymenttype'] = 'Manual';
                $insert['paymentmethodid'] = $row->id;
                $insert['status_payment'] = 'pending';

                if ($row->feetype != null) {
                    $feeamount = 0;
                    if ($row->feetype == 'Persentase') {
                        $feeamount = $fixproductprice * (((int) $row->nominalfee) / 100);
                    } else if ($row->feetype == 'Nominal') {
                        $feeamount = $row->nominalfee;
                    }

                    $insert['fee'] = $feeamount;
                    $fixproductprice = $fixproductprice + $feeamount;
                }

                $insert['price'] = $fixproductprice;
                $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($fixproductprice) . ",- Ke Rekening: " . $row->paymentmethod . ", no. $row->accountnumber a.n. $row->accountname. Batas waktu transfer 24 jam";
            } else if ($paymenttype == 'Otomatis') {
                $insert['paymenttype'] = 'Otomatis';
                $insert['status_payment'] = 'pending';

                if (!is_numeric($payment)) {
                    $json = json_decode($payment);

                    if (!is_object($json) && !isset($json->type)) {
                        $paymentgateway = $this->mspaymentgateway->get(array(
                            'userid' => $this->merchant->id,
                            'type' => 'Payment Gateway',
                            "(isdisabled IS NULL OR isdisabled = 0) =" => true
                        ))->row();

                        if ($paymentgateway == null) {
                            throw new Exception('Pembayaran tidak ditemukan');
                        } else if ($paymentgateway->isdisabled == 1) {
                            throw new Exception('Pembayaran yang anda pilih tidak aktif');
                        }

                        if ($paymentgateway->minnominal > $fixproductprice) {
                            throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->minnominal));
                        } else if ($paymentgateway->maxnominal != 0 && $paymentgateway->maxnominal < $fixproductprice) {
                            throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->maxnominal));
                        }

                        if ($paymentgateway != null && $paymentgateway->vendor == 'Midtrans' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->enabled_payments)) {
                                if (in_array($payment, $addons->enabled_payments) && isset(getMidtransPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $fixproductprice * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        $insert['fee'] = $feeamount;
                                        $fixproductprice = $fixproductprice + $feeamount;
                                    }

                                    Config::$serverKey = $detail->serverkey;
                                    Config::$clientKey = $detail->clientkey;
                                    Config::$isProduction = ENVIRONMENT == 'production' ? true : false;

                                    $params = array(
                                        'transaction_details' => array(
                                            'order_id' => $insert['clientcode'],
                                            'gross_amount' => $fixproductprice,
                                        ),
                                        'enabled_payments' => array(
                                            $payment
                                        )
                                    );

                                    try {
                                        $snapToken = Snap::getSnapToken($params);
                                    } catch (Exception $snapex) {
                                        if ($snapex->getMessage() != null) {
                                            log_message_user('error', "[MIDTRANS TOPUP] Response: " . $snapex->getMessage(), $this->merchant->id);
                                        }

                                        throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                    }

                                    $insert['payment'] = getMidtransPayments()[$payment];
                                    $insert['price'] = $fixproductprice;
                                    $insert['gatewayvendor'] = $paymentgateway->vendor;
                                    $insert['snaptoken'] = $snapToken;
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Tripay' && $paymentgateway->addons != null) {
                            if ($payment == 'OVO') {
                                if ($currentPhone == null) {
                                    throw new Exception('Nomor Handphone OVO wajib diisi');
                                } else if (!is_numeric($currentPhone)) {
                                    throw new Exception('Nomor Handphone OVO tidak valid');
                                } else {
                                    $insert['phonenumber_order'] = $currentPhone;
                                }
                            }

                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getTripayPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $fixproductprice * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        $insert['fee'] = $feeamount;
                                        $fixproductprice = $fixproductprice + $feeamount;
                                    }

                                    $tripay = new Tripay($detail->merchantcode, $detail->apikey, $detail->privatekey, ENVIRONMENT == 'production');
                                    $request = $tripay->requestTransaction($payment, $insert['clientcode'], $fixproductprice, $currentName, $CurrentEmail, $currentPhone, "Transaksi", 1, base_url('history?userid=' . $this->userid));

                                    if (isset($request->success) && $request->success) {
                                        $insert['payment'] = getTripayPayments()[$payment];
                                        $insert['price'] = $fixproductprice;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;
                                        $insert['checkouturl'] = $request->data->checkout_url;
                                        $insert['servercode_payment'] = $request->data->reference;
                                        $insert['jsonresponse_payment'] = json_encode($request);
                                    } else {
                                        if ($request != null) {
                                            log_message_user('error', '[TRIPAY TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                        }

                                        if (isset($request->message) && $request->message) {
                                            throw new Exception($request->message);
                                        } else {
                                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                        }
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'iPaymu' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getIpaymuPayments()[$payment])) {

                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $fixproductprice * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        $insert['fee'] = $feeamount;
                                        $fixproductprice = $fixproductprice + $feeamount;
                                    }

                                    $ipaymu = new iPaymu($detail->apikey, $detail->virtualaccount, ENVIRONMENT == 'production');
                                    $ipaymu->setURL([
                                        'ureturn' => base_url('landing'),
                                        'unotify' => base_url('callback/ipaymu')
                                    ]);

                                    $ipaymu->setBuyer([
                                        'name' => $currentName,
                                        'phone' => $currentPhone ?? '*************',
                                        'email' => $CurrentEmail
                                    ]);

                                    $ipaymu->setReferenceId($insert['clientcode']);
                                    $ipaymu->setPaymentChannel($payment);
                                    $ipaymu->setPaymentMethod(getIpaymuMethod()[$payment]);

                                    $carts = [];
                                    $carts = $ipaymu->add($insert['clientcode'], 'Transaksi', $fixproductprice, 1, "Transaksi senilai Rp " . IDR($fixproductprice), 0, 0, 0, 0);

                                    $ipaymu->addCart($carts);

                                    $request = $ipaymu->directPayment();

                                    if (isset($request['Status']) && $request['Status'] == 200) {
                                        $paymentNo = $request['Data']['PaymentNo'] ?? '- Unknown -';
                                        $paymentName = $request['Data']['PaymentName'] ?? '- Unknown -';
                                        $paymentChannel = $request['Data']['Channel'] ?? '- Unknown -';

                                        $insert['payment'] = getIpaymuPayments()[$payment];
                                        $insert['price'] = $fixproductprice;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;
                                        $insert['servercode_payment'] = $request['Data']['TransactionId'];
                                        $insert['jsonresponse_payment'] = json_encode($request);

                                        if ($payment != 'qris') {
                                            $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($fixproductprice) . ",- Ke Rekening: $paymentChannel, no. $paymentNo a.n $paymentName. Batas waktu transfer 24 jam";
                                        } else {
                                            $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($fixproductprice) . ",- Ke Rekening: $paymentChannel a.n $paymentName. Batas waktu transfer 24 jam";
                                            $insert['isqr'] = 1;
                                        }
                                    } else {
                                        if ($request != null) {
                                            log_message_user('error', '[IPAYMU TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                        }

                                        throw new Exception($request['Message'] ?? 'Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Duitku' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments) && isset($addons->channel_payments_name) && isset($detail->apikey) && isset($detail->merchantcode)) {
                                if (in_array($payment, $addons->channel_payments)) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $fixproductprice * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        $insert['fee'] = $feeamount;
                                        $fixproductprice = $fixproductprice + $feeamount;
                                    }

                                    $params = array(
                                        'paymentAmount' => $fixproductprice,
                                        'paymentMethod' => $payment,
                                        'merchantOrderId' => $insert['clientcode'],
                                        'productDetails' => 'Transkasi',
                                        'customerVaName' => $this->merchant->companyname,
                                        'email' => $CurrentEmail,
                                        'callbackUrl' => base_url('callback/duitku'),
                                        'returnUrl' => base_url('history?userid=' . $this->userid),
                                    );

                                    $apikey = $detail->apikey;
                                    $merchantcode = $detail->merchantcode;

                                    $duitku_config = new \Duitku\Config($apikey, $merchantcode, ENVIRONMENT == 'development', true, false);

                                    $paymentMethod = Api::getPaymentMethod($fixproductprice, $duitku_config);
                                    $paymentMethod = json_decode($paymentMethod);

                                    $paymentName = null;
                                    foreach ($paymentMethod->paymentFee as $key => $value) {
                                        if ($value->paymentMethod == $payment) {
                                            $paymentName = $value->paymentName;
                                        }
                                    }

                                    if ($paymentName == null) {
                                        throw new Exception('Pembayaran tidak ditemukan');
                                    }

                                    $create = Api::createInvoice($params, $duitku_config);
                                    $create = json_decode($create);

                                    if (isset($create->statusCode) && $create->statusCode == '00') {
                                        $insert['payment'] = $paymentName;
                                        $insert['price'] = $fixproductprice;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;
                                        $insert['checkouturl'] = $create->paymentUrl;
                                        $insert['servercode_payment'] = $create->reference;
                                        $insert['jsonresponse_payment'] = json_encode($create);
                                    } else {
                                        if ($create != null) {
                                            log_message_user('error', '[DUITKU TOPUP] Response: ' . json_encode($create), $this->merchant->id);
                                        }

                                        throw new Exception($create->statusMessage ?? 'Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Okeconnect' && $paymentgateway->addons != null) {

                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getOkeconnectPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $fixproductprice * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        $insert['fee'] = $feeamount;
                                        $fixproductprice = $fixproductprice + $feeamount;
                                    }

                                    if ($payment == 'MANDIRI' || $payment == 'MUAMALAT' || $payment == 'BRI' || $payment == 'BNI' || $payment == 'PERMATA' || $payment == 'BSI' || $payment == 'CIMB' || $payment == 'NEO' || $payment == 'DANAMON' || $payment == 'MAYBANK') {
                                        $type = 'VA';
                                    } else if ($payment == 'DANA' || $payment == 'OVO' || $payment == 'Gopay' || $payment == 'Shopee' || $payment == 'Link Aja' || $payment == 'All E-Wallet' || $payment == 'Mobile Bangking') {
                                        $type = 'EWALLET';
                                    } else if ($payment == 'ALFAMART' || $payment == 'INDOMARET') {
                                        $type = 'RETAIL';
                                    }

                                    $okeconnect = new Okeconnect($detail->merchantcode, $detail->apikey);
                                    $request = $okeconnect->requestTransaction($fixproductprice, $insert['clientcode'], 'Pembelian produk ' . $productRow->productname, $CurrentEmail, $payment, $currentPhone, null, base_url('history?userid=' . $this->userid), $type);

                                    if (($type == 'VA' && isset($request->status) && $request->status == TRUE) || ($type == 'RETAIL' && isset($request->success) && $request->success == TRUE && isset($request->response->status) && $request->response->status == 'success')) {
                                        $insert['payment'] = getOkeconnectPayments()[$payment];
                                        $insert['price'] = $fixproductprice;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;

                                        if ($type == 'VA') {
                                            $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($fixproductprice) . ",- Ke Rekening Virual Account: " . getOkeconnectPayments()[$payment] . ", no. " . $request->code->$payment . " Batas waktu transfer 24 jam";
                                            $insert['servercode_payment'] = $request->reff;
                                        } else if ($type == 'RETAIL') {
                                            $paymentresponse = strtolower($payment);
                                            $insert['note_payment'] = "Silahkan datang ke kasir " . getOkeconnectPayments()[$payment] . " dan bayar Okeconnect Rp " . IDR($fixproductprice) . ",dengan kode pembayaran " . $request->response->$paymentresponse->code . " Batas waktu transfer 24 jam";
                                            $insert['servercode_payment'] = $request->response->$paymentresponse->code;
                                        }

                                        $insert['jsonresponse_payment'] = json_encode($request);
                                    } else {
                                        if ($request != null) {
                                            log_message_user('error', '[Okeconnect TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                        }

                                        if (isset($request->message) && $request->message) {
                                            throw new Exception($request->message);
                                        } else {
                                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                        }
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else {
                            throw new Exception('Pembayaran tidak ditemukan');
                        }
                    } else {
                        if (isset($json->type) && $json->type == 'Notification Handler') {
                            $userbuynotificationhandler = $this->userbuynotificationhandler->select('a.*, b.packagename')
                                ->join('msnotificationhandlerpackage b', 'b.id = a.notificationid')
                                ->get(array(
                                    'a.userid' => $this->merchant->id,
                                    'a.id' => $json->id,
                                    'b.isactive' => 1,
                                    "(a.isdisabled IS NULL OR a.isdisabled = '0') =" => true,
                                ));

                            if ($userbuynotificationhandler->num_rows() == 0) {
                                throw new Exception('Pembayaran tidak ditemukan');
                            }

                            $rowuserbuynotificationhandler = $userbuynotificationhandler->row();

                            if ($rowuserbuynotificationhandler->isdisabled == 1) {
                                throw new Exception('Pembayaran yang anda pilih tidak aktif');
                            } else if ($rowuserbuynotificationhandler->minnominal > $fixproductprice) {
                                throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($rowuserbuynotificationhandler->minnominal));
                            } else if ($rowuserbuynotificationhandler->maxnominal != 0 && $rowuserbuynotificationhandler->maxnominal < $fixproductprice) {
                                throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($rowuserbuynotificationhandler->maxnominal));
                            }

                            if ($this->merchant->uniquenominal_start != null && $this->merchant->uniquenominal_end != null && $this->merchant->uniquenominal_start != 0 && $this->merchant->uniquenominal_end != 0) {
                                $unique = rand($this->merchant->uniquenominal_start, $this->merchant->uniquenominal_end);
                            } else {
                                $unique = rand(100, 999);
                            }

                            $fixproductprice = $fixproductprice + $unique;

                            $insert['payment'] = $rowuserbuynotificationhandler->packagename;
                            $insert['gatewayvendor'] = 'Notification Handler Services';
                            $insert['paymentmethodid'] = $rowuserbuynotificationhandler->id;
                            $insert['uniqueadmin'] = $unique;

                            if ($rowuserbuynotificationhandler->isfee == 1) {
                                $feeamount = 0;
                                if ($rowuserbuynotificationhandler->feetype == 'Persentase') {
                                    $feeamount = $fixproductprice * $rowuserbuynotificationhandler->nominalfee / 100;
                                } else if ($rowuserbuynotificationhandler->feetype == 'Nominal') {
                                    $feeamount = $rowuserbuynotificationhandler->nominalfee;
                                }

                                $insert['fee'] = $feeamount;
                                $fixproductprice = $fixproductprice + $feeamount;
                            }

                            $insert['price'] = $fixproductprice;
                            $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($fixproductprice) . ",- Ke Rekening: $rowuserbuynotificationhandler->packagename, no. $rowuserbuynotificationhandler->accountnumber a.n. $rowuserbuynotificationhandler->accountname. Batas waktu transfer 24 jam";
                        } else {
                            $paymentgateway = $this->mspaymentgateway->get(array(
                                'userid' => $this->merchant->id,
                                'type' => 'Payment Gateway',
                                "(isdisabled IS NULL OR isdisabled = 0) =" => true
                            ))->row();

                            if ($paymentgateway == null) {
                                throw new Exception('Pembayaran tidak ditemukan');
                            } else if ($paymentgateway->isdisabled == 1) {
                                throw new Exception('Pembayaran yang anda pilih tidak aktif');
                            }

                            if ($paymentgateway->minnominal > $fixproductprice) {
                                throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->minnominal));
                            } else if ($paymentgateway->maxnominal != 0 && $paymentgateway->maxnominal < $fixproductprice) {
                                throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->maxnominal));
                            }

                            if ($paymentgateway->vendor == 'PayDisini' && $paymentgateway->addons != null && isset($json->id)) {
                                $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                                $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                                $paydisini = new PayDisini($detail->apikey);
                                $paymentchannel = $paydisini->paymentChannel();

                                if (isset($paymentchannel->success) && $paymentchannel->success == true) {
                                    $paymentchannel = $paymentchannel->data;

                                    if (isset($addons->channel_payments)) {
                                        if (in_array($json->id, $addons->channel_payments) && in_array($json->id, array_column($paymentchannel, 'id'))) {
                                            $fee = $this->feepaymentgateway->row(array(
                                                'paymentgatewayid' => $paymentgateway->id,
                                                'paymentname' => $json->id,
                                                "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                            ));

                                            if ($fee != null) {
                                                $feeamount = 0;
                                                if ($fee->feetype == 'Persentase') {
                                                    $feeamount = $fixproductprice * $fee->fee / 100;
                                                } else if ($fee->feetype == 'Nominal') {
                                                    $feeamount = $fee->fee;
                                                }

                                                $insert['fee'] = $feeamount;
                                                $fixproductprice = $fixproductprice + $feeamount;
                                            }

                                            $fixproductprice = round($fixproductprice);
                                            $insert['paydisinicode'] = generateUniqueCodePayDisini('PRABAYAR-');

                                            $request = $paydisini->requestTransaction($json->id, $insert['paydisinicode'], $fixproductprice, 'Pembelian Produk ' . $productRow->productname, $currentPhone, $json->type);

                                            if (isset($request->success) && $request->success == TRUE) {
                                                $insert['payment'] = $addons->alias_payments[array_keys($addons->channel_payments, $json->id)[0] ?? ""] ?? $addons->channel_payments_name[array_search($json->id, $addons->channel_payments)];
                                                $insert['price'] = $fixproductprice;
                                                $insert['gatewayvendor'] = $paymentgateway->vendor;

                                                if ($json->type == 'VA') {
                                                    $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($fixproductprice) . ",- Ke Rekening: " . ($addons->alias_payments[array_keys($addons->channel_payments, $json->id)[0] ?? ""] ?? $addons->channel_payments_name[array_search($json->id, $addons->channel_payments)]) . ", no. " . $request->data->virtual_account . " Batas waktu transfer 3 jam hingga pukul" . DateFormat($request->data->expired, 'd F Y H:i:s');
                                                } else if ($json->type == 'Retail') {
                                                    $insert['note_payment'] = "Bayar di " . ($addons->alias_payments[array_keys($addons->channel_payments, $json->id)[0] ?? ""] ?? $addons->channel_payments_name[array_search($json->id, $addons->channel_payments)]) . " sebesar Rp " . IDR($fixproductprice) . ",- dan beritahu kasir kode pembayaran: " . $request->data->payment_code . " Batas waktu transfer 3 jam hingga pukul" . DateFormat($request->data->expired, 'd F Y H:i:s');
                                                } else {
                                                    $insert['note_payment'] = $request->data->note;
                                                }

                                                if ($json->type == 'QRIS') {
                                                    $insert['isqr'] = 1;
                                                }

                                                $insert['servercode_payment'] = $request->data->pay_id;
                                                $insert['checkouturl'] = $request->data->checkout_url;
                                                $insert['jsonresponse_payment'] = json_encode($request);
                                            } else {
                                                if ($request != null) {
                                                    log_message_user('error', '[PayDisini TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                                }

                                                if (isset($request->msg) && $request->msg) {
                                                    throw new Exception($request->msg);
                                                } else {
                                                    throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                                }
                                            }
                                        } else {
                                            throw new Exception('Pembayaran tidak ditemukan');
                                        }
                                    } else {
                                        throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                }
                            } else {
                                throw new Exception('Pembayaran tidak ditemukan');
                            }
                        }
                    }
                } else {
                    $paymentgateway = $this->mspaymentgateway->get(array(
                        'id' => $payment,
                        'userid' => $this->merchant->id,
                        "(isdisabled IS NULL OR isdisabled = 0) =" => true
                    ));

                    if ($paymentgateway->num_rows() == 0) {
                        throw new Exception('Pembayaran tidak ditemukan');
                    }

                    $rowpaymentgateway = $paymentgateway->row();

                    if ($rowpaymentgateway->isdisabled == 1) {
                        throw new Exception('Pembayaran yang anda pilih tidak aktif');
                    } else if ($rowpaymentgateway->minnominal > $fixproductprice) {
                        throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($rowpaymentgateway->minnominal));
                    } else if ($rowpaymentgateway->maxnominal != 0 && $rowpaymentgateway->maxnominal < $fixproductprice) {
                        throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($rowpaymentgateway->maxnominal));
                    }

                    $detail = json_decode(stringEncryption('decrypt', $rowpaymentgateway->detail));

                    $insert['payment'] = $rowpaymentgateway->type;

                    if ($this->merchant->uniquenominal_start != null && $this->merchant->uniquenominal_end != null && $this->merchant->uniquenominal_start != 0 && $this->merchant->uniquenominal_end != 0) {
                        $unique = rand($this->merchant->uniquenominal_start, $this->merchant->uniquenominal_end);
                        $insert['price'] = $fixproductprice + $unique;
                    } else {
                        $insert['price'] = $fixproductprice + rand(100, 999);
                    }

                    if ($rowpaymentgateway->feetype != null) {
                        $feeamount = 0;

                        if ($rowpaymentgateway->feetype == 'Persentase') {
                            $feeamount = $fixproductprice * $rowpaymentgateway->nominalfee / 100;
                        } else if ($rowpaymentgateway->feetype == 'Nominal') {
                            $feeamount = $rowpaymentgateway->nominalfee;
                        }

                        $insert['fee'] = $feeamount;
                        $insert['price'] = $insert['price'] + $feeamount;
                    }

                    if ($rowpaymentgateway->vendor == 'BCA') {
                        $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($insert['price']) . ",- Ke Rekening: BCA, no. $detail->accountnumber a.n. $detail->accountname. Batas waktu transfer 24 jam";
                    } else if ($rowpaymentgateway->vendor == 'GOPAY') {
                        $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($insert['price']) . ",- Ke Rekening: GOPAY, no. $detail->phonenumber. Batas waktu transfer 24 jam";
                    } else if ($rowpaymentgateway->vendor == 'OVO') {
                        $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($insert['price']) . ",- Ke Rekening: OVO, no. $detail->phonenumber. Batas waktu transfer 24 jam";
                    }
                }
            } else {
                throw new Exception('Pembayaran tidak ditemukan');
            }

            $originprice = $productRow->price;
            $potonganprofit = $originprice - $fixproductprice_forprofit;

            if ($productRow->isstock == 1) {
                $this->msproduct->update(array(
                    'id' => $productRow->id
                ), array(
                    'stock' => $productRow->stock - 1
                ));
            }

            $insert['serviceid'] = $productRow->id;
            $insert['target'] = $target;
            $insert['profit'] = ($productRow->profit - $potonganprofit);
            $insert['paymenttype'] = $paymenttype;
            $insert['status'] = 'pending';
            $insert['type'] = 'PPOB';
            $insert['queuetransaction'] = $queuetransaction ? 1 : 0;
            $insert['category_apikey'] = $productRow->category_apikey;
            $insert['subcategory_apikey'] = $productRow->subcategory_apikey;
            $insert['orderplatform'] = 'web';
            $insert['productcode'] = $productRow->code;
            $insert['productname_order'] = $productRow->productname;
            $insert['vendor'] = $productRow->vendor;
            $insert['email_order'] = $CurrentEmail;
            $insert['phonenumber_order'] = $currentPhone;
            $insert['merchantid_order'] = $this->merchant->id;
            $insert['vendorid'] = $productRow->vendorid;

            if ($productRow->vendor != null) {
                if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand)) && $productRow->vendor == 'VIPayment') {
                    $insert['zoneid'] = $zoneid;
                }
            }

            $this->trorder->insert($insert);
            $orderid = $this->db->insert_id();

            if ($this->db->trans_status() == FALSE) {
                throw new Exception('Transaksi gagal');
            }

            pullTransaction($this->merchant->id);

            if (isLogin() && $paymenttype == 'Saldo') {
                $inserthistorybalance = array();
                $inserthistorybalance['userid'] = getCurrentIdUser();
                $inserthistorybalance['type'] = 'OUT';
                $inserthistorybalance['nominal'] = $fixproductprice;
                $inserthistorybalance['currentbalance'] = $currentuser->balance;
                $inserthistorybalance['orderid'] = $orderid;
                $inserthistorybalance['createdby'] = getCurrentIdUser();
                $inserthistorybalance['createddate'] = getCurrentDate();

                $this->historybalance->insert($inserthistorybalance);

                $update = array();
                $update['balance'] = $currentuser->balance - $fixproductprice;

                $this->msusers->update(array(
                    'id' => getCurrentIdUser()
                ), $update);

                if ($queuetransaction == false && $productRow->vendor != null && ENVIRONMENT == 'production') {
                    if ($this->merchant->multivendor != 1) {
                        if ($vendor == 'Digiflazz') {
                            $digiflazz = new Digiflazz(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));

                            $servercode = generateTransactionNumber('ORDER');
                            $topup = $digiflazz->topup($productRow->code, $target, $servercode);

                            $result = json_decode($topup);

                            if (isset($result->data->status)) {
                                if ($result->data->status != 'Gagal') {
                                    $update = array();
                                    $update['servercode'] = $servercode;
                                    $update['note'] = $result->data->message;
                                    $update['sn'] = $result->data->sn;
                                    $update['status'] = strtolower($result->data->status);
                                    $update['jsonresponse'] = json_encode($result);
                                    $update['status_payment'] = 'sukses';

                                    $this->trorder->update(array(
                                        'id' => $orderid
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($orderid, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($result != null) {
                                        log_message_user('error', '[DIGIFLAZZ PRABAYAR] Response: ' . json_encode($result), $this->merchant->id);
                                    }

                                    $responsecode = getResponseCodeDigiflazz($result->data->rc);
                                    throw new Exception($responsecode);
                                }
                            } else {
                                throw new Exception('Transaksi gagal');
                            }
                        } else if ($vendor == 'VIPayment') {
                            $vipayment = new VIPayment(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));

                            if ($productRow->vendor == 'VIPayment' && $productRow->type == 'game') {
                                if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand))) {
                                    $order = $vipayment->order_game($productRow->code, $target, $zoneid);
                                } else {
                                    $order = $vipayment->order_game($productRow->code, $target, null);
                                }
                            } else {
                                $order = $vipayment->order_prepaid($productRow->code, $target);
                            }

                            if ($order->result) {
                                $update = array();
                                $update['servercode'] = $order->data->trxid;
                                $update['jsonresponse'] = json_encode($order);
                                $update['status_payment'] = 'sukses';

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($orderid, $this->merchant->id, $currentPhone);

                                return JSONResponse(array(
                                    'RESULT' => 'OK',
                                    'MESSAGE' => $order->message,
                                    'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                ));
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[VIPAYMENT PRABAYAR] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->message);
                            }
                        }
                    } else {
                        $order = $this->msvendordetail->select('a.*, b.default_config')
                            ->join('msvendor b', 'b.id = a.vendorid')
                            ->get(array(
                                'a.vendorid' => $productRow->vendorid,
                                'a.apitype' => 'Order'
                            ));

                        if ($order->num_rows() == 0) {
                            throw new Exception('Konfigurasi vendor tidak ditemukan');
                        }

                        $vendor_order = $order->row();

                        if ($vendor_order->default_config == null) {
                            throw new Exception('Konfigurasi vendor tidak ditemukan');
                        }

                        $response_indicator = json_decode($vendor_order->response_indicator);
                        $response_setting = json_decode($vendor_order->response_setting);

                        $dynamicvendor = new DynamicVendor($productRow->vendorid, json_decode($vendor_order->default_config, true));
                        $order = $dynamicvendor->order($orderid);

                        if (
                            ($response_indicator->index == null && isset($order[$response_indicator->key]) && (
                                $response_indicator->datatype == 'string' && $order[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'number' && $order[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'boolean' && $order[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->key])
                            ))
                            || ($response_indicator->index != null && isset($order[$response_indicator->index][$response_indicator->key]) && (
                                $response_indicator->datatype == 'string' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'number' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'boolean' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->index][$response_indicator->key])
                            ))
                        ) {
                            $var_referenceid = $response_setting->referenceid ?? null;
                            $var_price = $response_setting->price ?? null;
                            $var_status = $response_setting->status ?? null;
                            $var_note = $response_setting->note ?? null;
                            $var_sn = $response_setting->sn ?? null;
                            $var_errorrefund = $response_setting->errorrefund;

                            $exploding_errorefund = explode('[#]', strtolower($var_errorrefund ?? ''));

                            if ($response_setting->index != null) {
                                $order = $order[$response_setting->index] ?? null;
                            }

                            $referenceid = $var_referenceid != null ? ($order[$var_referenceid] ?? null) : null;
                            $price = $var_price != null ? ($order[$var_price] ?? null) : null;
                            $status = $var_status != null ? ($order[$var_status] ?? null) : null;
                            $note = $var_note != null ? ($order[$var_note] ?? null) : null;
                            $sn = $var_sn != null ? ($order[$var_sn] ?? null) : null;

                            if ($status != null) {
                                if (in_array($status, $exploding_errorefund)) {
                                    log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing error refund, response: ' . json_encode($order), $this->merchant->id);

                                    throw new Exception('Transaksi gagal');
                                }
                            } else {
                                if ($var_status == null) {
                                    $status = 'pending';
                                } else if ($var_status != null && $status == null) {
                                    log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing status, response: ' . json_encode($order), $this->merchant->id);

                                    throw new Exception('Transaksi gagal');
                                }
                            }

                            $update = array();
                            $update['jsonresponse'] = json_encode($order);

                            if ($referenceid != null) {
                                $update['servercode'] = $referenceid;
                            }

                            if ($price != null) {
                                $update['price'] = $price;
                            }

                            if ($status != null) {
                                $update['status'] = $status;
                            }

                            if ($note != null) {
                                $update['note'] = $note;
                            }

                            if ($sn != null) {
                                $update['sn'] = $sn;
                            }

                            $this->trorder->update(array(
                                'id' => $orderid
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($orderid, $this->merchant->id, $currentuser->phonenumber);

                            return JSONResponse(array(
                                'RESULT' => 'OK',
                                'MESSAGE' => 'Transaksi berhasil',
                                'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                            ));
                        } else {
                            log_message_user('error', '[MULTIVENDOR ORDER] Failed to parse response, response: ' . json_encode($order), $this->merchant->id);

                            throw new Exception('Transaksi gagal');
                        }
                    }
                }

                if ($productRow->isdatabase == 1 && $productRow->databasecategory == 'Stock Product') {
                    $stockproductdetail = $this->msstockproductdetail->get(array(
                        'stockproductid' => $databaseid,
                        'status' => 'Tersedia'
                    ))->row();

                    $updatestockproductdetail = array();
                    $updatestockproductdetail['status'] = 'Tidak Tersedia';
                    $updatestockproductdetail['orderid'] = $orderid;

                    $this->msstockproductdetail->update(array(
                        'id' => $stockproductdetail->id
                    ), $updatestockproductdetail);

                    $update = array();
                    $update['status'] = 'success';
                    $update['sn'] = stringEncryption('decrypt', $stockproductdetail->data);

                    $this->trorder->update(array(
                        'id' => $orderid
                    ), $update);
                }
            }

            $this->db->trans_commit();

            $this->send_notification($orderid, $this->merchant->id, $currentPhone);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'MESSAGE' => 'Transaksi berhasil',
                'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
            ));
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function smm_category()
    {
        if (getCurrentThemeConfiguration($this->merchant->id) != 'Fin-App' && getCurrentThemeConfiguration($this->merchant->id) != 'Sobat-Serverppob') {
            return show_404();
        }

        if ($this->merchant->companycategory != 'SMM' && $this->merchant->companycategory != 'PPOB & SMM') {
            return JSONResponse(array(
                'RESULT' => 'ERROR',
                'MESSAGE' => 'Anda tidak memiliki akses ke halaman ini.'
            ));
        }

        $vendor = getCurrentVendor('SMM', $this->merchant->id);

        $this->db->select('a.name as category, b.asseturl, "SMM" as vendor');
        $this->db->from('msplatformsosmed a');
        $this->db->join('msicons b', 'b.id = a.assetid');
        $this->db->join('msdetailplatform c', 'c.platformid = a.id');
        $this->db->join('msproduct d', 'd.category = c.category');

        $this->db->where(array(
            'a.userid' => $this->merchant->id,
            'd.vendor' => $vendor
        ));
        $this->db->group_by('a.name');

        $query = $this->db->get()->result();

        $data = array();
        $data['category'] = $query;

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => viewTemplate($this->merchant->id, "landing/smm/category", $data, true)
        ));
    }

    public function smm_subcategory()
    {
        try {
            if (getCurrentThemeConfiguration($this->merchant->id) != 'Fin-App' && getCurrentThemeConfiguration($this->merchant->id) != 'Sobat-Serverppob') {
                return show_404();
            }

            $category = getPost('category');
            $searchfilter = getPost('searchfilter');

            $cekcategory = $this->msplatformsosmed->select('a.name')
                ->where(array(
                    'a.userid' => $this->merchant->id,
                    'a.name' => $category
                ))
                ->get();

            if ($cekcategory->num_rows() == 0) {
                throw new Exception('Kategori tidak ditemukan');
            } else {
                $row = $cekcategory->row();

                $disabled = $this->disabledcategory->result(array(
                    'userid' => $this->merchant->id,
                    'category_apikey' => 'SMM'
                ));

                $disabled_category = array();
                foreach ($disabled as $key => $value) {
                    $disabled_category[] = $value->categoryname;
                }

                $where = array(
                    'a.platformid' => $row->id,
                    'b.userid' => $this->merchant->id
                );

                if ($searchfilter != null) {
                    $where['a.category LIKE'] = '%' . $searchfilter . '%';
                }

                $get = $this->msdetailplatform->select('a.category, c.asseturl')
                    ->join('msplatformsosmed b', 'b.id = a.platformid')
                    ->join('msicons c', 'c.id = b.assetid', 'LEFT')
                    ->join('msproduct d', 'd.category = a.category')
                    ->where_not_in('a.category', $disabled_category)
                    ->group_by('a.category, c.asseturl')
                    ->get($where);

                $data = array();
                $data['subcategory'] = $get->result();
                $data['category'] = $category;

                return JSONResponse(array(
                    'RESULT' => 'OK',
                    'CONTENT' => viewTemplate($this->merchant->id, "landing/smm/subcategory", $data, true),
                    'COUNTCATEGORY' => $get->num_rows()
                ));
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function smm_product()
    {
        if (getCurrentThemeConfiguration($this->merchant->id) != 'Fin-App' && getCurrentThemeConfiguration($this->merchant->id) != 'Sobat-Serverppob') {
            return show_404();
        }

        $key = getGet('key');
        $key = stringEncryption('decrypt', $key);


        if ($key == null) {
            return show_404();
        }

        $key = json_decode($key, true);

        if (!isset($key['category']) || !isset($key['subcategory'])) {
            return show_404();
        }

        $category = $key['category'];
        $subcategory = $key['subcategory'];

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'SMM'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.subcategory_apikey' => 'SMM',
            'a.category' => $subcategory,
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('SMM', $this->merchant->id);
            $where["(a.vendor = '$vendor' OR a.vendor IS NULL) ="] = true;
            $where['a.vendorid'] = null;
        } else {
            $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
        }

        $this->msproduct->where_not_in('category', $disabled_category)
            ->order_by('a.price', 'ASC');

        $get = $this->msproduct->select('a.*')
            ->join('brandimage b', 'b.brandname = a.brand AND b.userid = a.userid AND b.categoryname = a.category', 'LEFT')
            ->join('msicons c', 'c.id = b.assetid', 'LEFT')
            ->where($where)
            ->group_by('a.id')
            ->get();

        if ($get->num_rows() == 0) {
            return show_404();
        }

        if (isLogin()) {
            $currentuser = getCurrentUser();

            if (($currentuser->roleid ?? null) == null) {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'id' => $currentuser->roleid
                ))->row();
            }
        } else {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'isdefault' => '1'
            ))->row();
        }

        if ($getrole != null) {
            if ($getrole->discounttype == 'Simple') {
                $getrolediscount = $this->msrolediscount->get(array(
                    'userid' => $this->merchant->id,
                    'roleid' => $getrole->id,
                ))->row();

                $discount = $getrolediscount->trxdiscount ?? 0;
            } else {
                $discount = $this->msrolediscountadv->get(array(
                    'createdby' => $this->merchant->id,
                    'roleid' => $getrole->id,
                    'servicetype' => 'SMM'
                ))->result();
            }
        } else {
            $getrole = new stdClass();
            $getrole->discounttype = 'Simple';
            $discount = 0;
        }

        $getdetailplatform = $this->msdetailplatform->select('a.category,c.asseturl')
            ->join('msplatformsosmed b', 'b.id = a.platformid')
            ->join('msicons c', 'c.id = b.assetid', 'LEFT')
            ->where(array(
                'a.category' => $subcategory,
                'b.name' => $category,
                'b.userid' => $this->merchant->id
            ))
            ->get()
            ->row();

        $data = array();
        $data['title'] = 'Product';
        $data['content'] = 'landing/smm/product';
        $data['product'] = $get->result();
        $data['row'] = $get->row();
        $data['discount'] = $discount;
        $data['getrole'] = $getrole;
        $data['smm'] = $getdetailplatform;
        $data['back'] = base_url('?userid=' . $this->userid);

        $paymentgateway = $this->mspaymentgateway->result(array(
            'userid' => $this->merchant->id,
            "(isdisabled IS NULL OR isdisabled = 0) =" => true
        ));

        $addons_select = array();
        foreach ($paymentgateway as $key => $value) {
            if ($value->type == 'Payment Gateway') {
                $feepaymentgateway = $this->feepaymentgateway->result(array(
                    'paymentgatewayid' => $value->id,
                    'vendor' => $value->vendor,
                ));

                $fee = array();
                foreach ($feepaymentgateway as $k => $v) {
                    $fee[$v->paymentname] = array(
                        'fee' => round($v->fee),
                        'type' => $v->feetype,
                        'paymentimage' => $v->paymentimage ?? null
                    );
                }

                if ($value->vendor == 'Midtrans') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->enabled_payments)) {
                        foreach ($addons->enabled_payments as $k => $v) {
                            if (isset(getMidtransPayments()[$v])) {
                                $select = array(
                                    'id' => stringEncryption('encrypt', $v),
                                    'data' => getMidtransPayments()[$v],
                                    'nominalfee' => $fee[$v]['fee'] ?? 0,
                                    'feetype' => $fee[$v]['type'] ?? 'Nominal',
                                    'paymentimage' => $fee[$v]['paymentimage'] ?? null,
                                    'minnominal' => $value->minnominal,
                                    'maxnominal' => $value->maxnominal
                                );
                                array_push($addons_select, $select);
                            }
                        }
                    }
                } elseif ($value->vendor == 'Tripay') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            if (isset(getTripayPayments()[$v])) {
                                $select = array(
                                    'id' => stringEncryption('encrypt', $v),
                                    'data' => $addons->alias_payments[$k] ?? getTripayPayments()[$v],
                                    'nominalfee' => $value->nominalfee,
                                    'feetype' => $value->feetype,
                                    'paymentimage' => $fee[$v]['paymentimage'] ?? null,
                                    'minnominal' => $value->minnominal,
                                    'maxnominal' => $value->maxnominal
                                );
                                array_push($addons_select, $select);
                            }
                        }
                    }
                } elseif ($value->vendor == 'iPaymu') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            if (isset(getiPaymuPayments()[$v])) {
                                $select = array(
                                    'id' => stringEncryption('encrypt', $v),
                                    'data' => $addons->alias_payments[$k] ?? getiPaymuPayments()[$v],
                                    'nominalfee' => $fee[$v]['fee'] ?? 0,
                                    'feetype' => $fee[$v]['type'] ?? 'Nominal',
                                    'paymentimage' => $fee[$v]['paymentimage'] ?? null,
                                    'minnominal' => $value->minnominal,
                                    'maxnominal' => $value->maxnominal
                                );
                                array_push($addons_select, $select);
                            }
                        }
                    }
                } elseif ($value->vendor == 'Duitku') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments) && isset($addons->channel_payments_name)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            $select = array(
                                'id' => stringEncryption('encrypt', $v),
                                'data' => $addons->alias_payments[$k] ?? $addons->channel_payments_name[$k],
                                'nominalfee' => $fee[$v]['fee'] ?? 0,
                                'feetype' => $fee[$v]['type'] ?? 'Nominal',
                                'paymentimage' => $fee[$v]['paymentimage'] ?? null,
                                'minnominal' => $value->minnominal,
                                'maxnominal' => $value->maxnominal
                            );
                            array_push($addons_select, $select);
                        }
                    }
                } elseif ($value->vendor == 'Okeconnect') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments) && isset($addons->channel_payments_name)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            $select = array(
                                'id' => stringEncryption('encrypt', $v),
                                'data' => $addons->alias_payments[$k] ?? getOkeconnectPayments()[$v],
                                'nominalfee' => $fee[$v]['fee'] ?? 0,
                                'feetype' => $fee[$v]['type'] ?? 'Nominal',
                                'paymentimage' => $fee[$v]['paymentimage'] ?? null,
                                'minnominal' => $value->minnominal,
                                'maxnominal' => $value->maxnominal,
                            );
                            array_push($addons_select, $select);
                        }
                    }
                } else if ($value->vendor == 'PayDisini') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments) && isset($addons->channel_payments_name)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            $select = array(
                                'id' => stringEncryption('encrypt', json_encode(array('id' => $v, 'type' => $addons->channel_payments_type[$k]))),
                                'data' => $addons->alias_payments[$k] ?? $addons->channel_payments_name[$k],
                                'nominalfee' => $fee[$v]['fee'] ?? 0,
                                'feetype' => $fee[$v]['type'] ?? 'Nominal',
                                'paymentimage' => $fee[$v]['paymentimage'] ?? null,
                                'minnominal' => $value->minnominal,
                                'maxnominal' => $value->maxnominal,
                            );
                            array_push($addons_select, $select);
                        }
                    }
                }
            } else {
                $select = array(
                    'id' => stringEncryption('encrypt', $value->id),
                    'data' => $value->type,
                    'nominalfee' => $value->nominalfee,
                    'feetype' => $value->feetype,
                    'paymentimage' => $value->paymentimage ?? null,
                    'minnominal' => $value->minnominal,
                    'maxnominal' => $value->maxnominal
                );
                array_push($addons_select, $select);
            }
        }

        $notificationhandler = $this->userbuynotificationhandler->select('a.*, b.packagename')
            ->join('msnotificationhandlerpackage b', 'b.id = a.notificationid')
            ->result(array(
                'a.userid' => $this->merchant->id,
                'b.isactive' => 1,
                "(isdisabled IS NULL OR isdisabled = 0) =" => true
            ));

        foreach ($notificationhandler as $key => $value) {
            $keys = stringEncryption('encrypt', json_encode(array(
                'id' => $value->id,
                'type' => 'Notification Handler',
            )));

            $select = array(
                'id' => $keys,
                'data' => $value->packagename,
                'minnominal' => $value->minnominal,
                'maxnominal' => $value->maxnominal,
                'nominalfee' => $value->isfee == 1 ? $value->nominalfee : 0,
                'feetype' => $value->feetype,
                'paymentimage' =>  $value->paymentimage ?? null
            );
            array_push($addons_select, $select);
        }

        $payment = $this->mspaymentmethod->result(array(
            'userid' => $this->merchant->id,
            "(isdisabled = 0 OR isdisabled IS NULL) =" => true
        ));

        asort($addons_select);
        $data['paymentotomatis'] = $addons_select;
        $data['paymentmanual'] =  $payment;

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function confirm_order_smm()
    {
        if (getCurrentThemeConfiguration($this->merchant->id) != 'Fin-App' && getCurrentThemeConfiguration($this->merchant->id) != 'Sobat-Serverppob') {
            return show_404();
        }

        $phoneUtil = PhoneNumberUtil::getInstance();

        $product = getPost('product');
        $additional = getPost('additional');
        $target = getPost('target');
        $qty = getPost('qty');
        $pin = getPost('pin');
        $paymenttype = getPost('paymentmethodtype');
        $payment = getPost('paymentmethod');
        $phonenumber = getPost('phonenumber');
        $email = getPost('email');

        if ($product == null) {
            throw new Exception('Produk tidak ditemukan');
        } else if ($target == null) {
            throw new Exception('Target wajib diisi');
        } else if ($qty == null) {
            throw new Exception('Jumlah wajib diisi');
        } else if (!is_numeric($qty)) {
            throw new Exception('Jumlah harus berupa angka');
        } else if ($phonenumber == null) {
            throw new Exception('Nomor Telepon wajib diisi');
        } else if ($paymenttype == null) {
            throw new Exception('Tipe Pembayaran tidak ditemukan');
        } else if ($payment == null) {
            throw new Exception('Metode Pembayaran tidak ditemukan');
        } else if ($email == null) {
            throw new Exception('Email wajib diisi');
        } else {
            try {
                $idNumberProto = $phoneUtil->parse($phonenumber, 'ID');
                $isValid = $phoneUtil->isValidNumber($idNumberProto);

                if (!$isValid) {
                    throw new Exception('Nomor Telepon yang anda masukkan tidak valid');
                }

                $phonenumber = $phoneUtil->format($idNumberProto, PhoneNumberFormat::E164);
                $phonenumber = substr($phonenumber, 1);
            } catch (NumberParseException $ex) {
                throw new Exception('Nomor Telepon yang anda masukkan tidak valid');
            }
        }

        $payment = stringEncryption('decrypt', $payment);
        $product = stringEncryption('decrypt', $product);

        $currentName = $target;
        $currentPhone = $phonenumber;
        $CurrentEmail = $email ?? null;

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'SMM'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'id' => $product,
            'userid' => $this->merchant->id,
            'subcategory_apikey' => 'SMM',
            'category !=' => null
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('SMM', $this->merchant->id);
            $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
            $where['vendorid'] = null;
        } else {
            $where["((vendorid IS NOT NULL vendorenabled = 1) OR vendor IS NULL) ="] = true;
        }

        $getproduct = $this->msproduct->where_not_in('category', $disabled_category)
            ->get($where);

        if ($getproduct->num_rows() == 0) {
            throw new Exception('Produk tidak ditemukan');
        }

        $productRow = $getproduct->row();

        $getdetailplatform = $this->msdetailplatform->select('a.category,c.asseturl')
            ->join('msplatformsosmed b', 'b.id = a.platformid')
            ->join('msicons c', 'c.id = b.assetid', 'LEFT')
            ->where(array(
                'a.category' => $productRow->category,
                'b.userid' => $this->merchant->id
            ))
            ->get()
            ->row();

        if ($productRow->status != 1) {
            throw new Exception('Produk tidak aktif');
        }

        if ($productRow->minorder > $qty) {
            throw new Exception("Jumlah minimal order adalah " . IDR($productRow->minorder));
        }

        if ($productRow->maxorder < $qty) {
            throw new Exception("Jumlah maksimal order adalah " . IDR($productRow->maxorder));
        }

        if (isLogin()) {
            if ($pin == null) {
                throw new Exception('PIN Transaksi wajib diisi');
            }

            $currentuser = getCurrentUser();
            $currentName = $currentuser->name;
            $CurrentEmail = $currentuser->email;

            if (stringEncryption('encrypt', $pin) != $currentuser->pin) {
                throw new Exception('PIN Transaksi yang anda masukkan salah');
            }

            if ($currentuser->roleid == null) {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'id' => $currentuser->roleid
                ))->row();
            }

            if ($getrole != null) {
                if ($getrole->discounttype == 'Simple') {
                    $getrolediscount = $this->msrolediscount->get(array(
                        'userid' => $this->merchant->id,
                        'roleid' => $getrole->id
                    ))->row();

                    $discount = $getrolediscount->trxdiscount ?? 0;
                } else {
                    $discount = $this->msrolediscountadv->get(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        'servicetype' => 'SMM'
                    ))->result();
                }
            } else {
                $getrole = new stdClass();
                $getrole->discounttype = 'Simple';
                $discount = 0;
            }

            $getpending = $this->trorder->total(array(
                'userid' => getCurrentIdUser(),
                'serviceid' => $productRow->id,
                'status' => 'pending',
                'target' => $target
            ));

            if ($getpending > 0) {
                throw new Exception('Anda memiliki transaksi yang masih dalam proses');
            }
        } else {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'isdefault' => '1'
            ))->row();

            if ($getrole != null) {
                if ($getrole->discounttype == 'Simple') {
                    $getrolediscount = $this->msrolediscount->get(array(
                        'userid' => $this->merchant->id,
                        'roleid' => $getrole->id
                    ))->row();

                    $discount = $getrolediscount->trxdiscount ?? 0;
                } else {
                    $discount = $this->msrolediscountadv->get(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        'servicetype' => 'SMM'
                    ))->result();
                }
            } else {
                $getrole = new stdClass();
                $getrole->discounttype = 'Simple';
                $discount = 0;
            }

            $getpending = $this->trorder->total(array(
                'clientip' => get_client_ip(),
                'serviceid' => $productRow->id,
                'status' => 'pending',
                'target' => $target
            ));

            if ($getpending > 0) {
                throw new Exception('Anda memiliki transaksi yang masih dalam proses');
            }
        }

        $fixproductprice = 0;

        if ($getrole->discounttype == 'Simple') {
            $fixproductprice = $productRow->price - $discount;
        } else {
            $found = false;

            foreach ($discount as $val) {
                if ($found) continue;

                if ($val->startrange <= $productRow->price && $val->endrange >= $productRow->price) {
                    if ($val->discounttype == 'Persentase') {
                        $fixproductprice = $productRow->price - ($productRow->price * $val->nominal / 100);

                        $found = true;
                    } else {
                        $fixproductprice = $productRow->price - $val->nominal;

                        $found = true;
                    }
                }
            }

            if ($found == false) {
                $fixproductprice = $productRow->price;
            }
        }

        $totalharga = round(($fixproductprice / 1000) * $qty);
        $totalhargaorigin = ($productRow->price / 1000) * $qty;

        $potonganprofit = $totalhargaorigin - $totalharga;

        $totalhargabyvendor = ($productRow->vendorprice / 1000) * $qty;
        $profit = (($productRow->profit / 1000) * $qty) - $potonganprofit;

        $data = array();
        $paymentimage = null;

        if ($paymenttype == 'Saldo' && isLogin()) {
            if (getCurrentBalance() < $totalharga) {
                throw new Exception('Saldo anda tidak mencukupi');
            }
        } else if ($paymenttype == 'Manual') {

            $get = $this->mspaymentmethod->get(array(
                'userid' => $this->merchant->id,
                'id' => $payment,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Pembayaran tidak ditemukan');
            }

            $row = $get->row();

            if ($row->isdisabled == 1) {
                throw new Exception('Pembayaran yang anda pilih tidak aktif');
            } elseif ($row->minnominal > $totalharga) {
                throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($row->minnominal));
            } else if ($row->maxnominal != 0 && $row->maxnominal < $totalharga) {
                throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($row->maxnominal));
            }

            if ($row->feetype != null) {
                $feeamount = 0;

                if ($row->feetype == 'Persentase') {
                    $feeamount = $totalharga * (((int) $row->nominalfee) / 100);
                } else if ($row->feetype == 'Nominal') {
                    $feeamount = $row->nominalfee;
                }
            }

            $paymentimage = $row->paymentimage;
        } else if ($paymenttype == 'Otomatis') {

            if (!is_numeric($payment)) {
                $json = json_decode($payment);

                if (!is_object($json) && !isset($json->type)) {
                    $paymentgateway = $this->mspaymentgateway->get(array(
                        'userid' => $this->merchant->id,
                        'type' => 'Payment Gateway',
                        "(isdisabled IS NULL OR isdisabled = 0) =" => true
                    ))->row();

                    if ($paymentgateway == null) {
                        throw new Exception('Pembayaran tidak ditemukan');
                    } else if ($paymentgateway->isdisabled == 1) {
                        throw new Exception('Pembayaran yang anda pilih tidak aktif');
                    }

                    if ($paymentgateway->minnominal > $totalharga) {
                        throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->minnominal));
                    } else if ($paymentgateway->maxnominal != 0 && $paymentgateway->maxnominal < $totalharga) {
                        throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->maxnominal));
                    }

                    if ($paymentgateway != null && $paymentgateway->vendor == 'Midtrans' && $paymentgateway->addons != null) {
                        $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                        $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                        if (isset($addons->enabled_payments)) {
                            if (in_array($payment, $addons->enabled_payments) && isset(getMidtransPayments()[$payment])) {
                                $fee = $this->feepaymentgateway->row(array(
                                    'paymentgatewayid' => $paymentgateway->id,
                                    'paymentname' => $payment,
                                    "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                ));

                                if ($fee != null) {
                                    $feeamount = 0;
                                    if ($fee->feetype == 'Persentase') {
                                        $feeamount = $totalharga * $fee->fee / 100;
                                    } else if ($fee->feetype == 'Nominal') {
                                        $feeamount = $fee->fee;
                                    }
                                }

                                $paymentimage = $fee->paymentimage;
                            } else {
                                throw new Exception('Pembayaran tidak ditemukan');
                            }
                        } else {
                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                        }
                    } else if ($paymentgateway != null && $paymentgateway->vendor == 'Tripay' && $paymentgateway->addons != null) {
                        if ($payment == 'OVO') {
                            if ($currentPhone == null) {
                                throw new Exception('Nomor Handphone OVO tidak boleh kosong');
                            } else if (!is_numeric($currentPhone)) {
                                throw new Exception('Nomor Handphone OVO tidak valid');
                            }
                        }

                        $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                        $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                        if (isset($addons->channel_payments)) {
                            if (in_array($payment, $addons->channel_payments) && isset(getTripayPayments()[$payment])) {
                                $fee = $this->feepaymentgateway->row(array(
                                    'paymentgatewayid' => $paymentgateway->id,
                                    'paymentname' => $payment,
                                    "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                ));

                                if ($fee != null) {
                                    $feeamount = 0;
                                    if ($fee->feetype == 'Persentase') {
                                        $feeamount = $totalharga * $fee->fee / 100;
                                    } else if ($fee->feetype == 'Nominal') {
                                        $feeamount = $fee->fee;
                                    }
                                }

                                $paymentimage = $fee->paymentimage;
                            } else {
                                throw new Exception('Pembayaran tidak ditemukan');
                            }
                        } else {
                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                        }
                    } else if ($paymentgateway != null && $paymentgateway->vendor == 'iPaymu' && $paymentgateway->addons != null) {
                        $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                        $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                        if (isset($addons->channel_payments)) {
                            if (in_array($payment, $addons->channel_payments) && isset(getIpaymuPayments()[$payment])) {
                                $fee = $this->feepaymentgateway->row(array(
                                    'paymentgatewayid' => $paymentgateway->id,
                                    'paymentname' => $payment,
                                    "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                ));

                                if ($fee != null) {
                                    $feeamount = 0;
                                    if ($fee->feetype == 'Persentase') {
                                        $feeamount = $totalharga * $fee->fee / 100;
                                    } else if ($fee->feetype == 'Nominal') {
                                        $feeamount = $fee->fee;
                                    }
                                }

                                $paymentimage = $fee->paymentimage;
                            } else {
                                throw new Exception('Pembayaran tidak ditemukan');
                            }
                        } else {
                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                        }
                    } else if ($paymentgateway != null && $paymentgateway->vendor == 'Duitku' && $paymentgateway->addons != null) {
                        $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                        $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                        if (isset($addons->channel_payments) && isset($addons->channel_payments_name) && isset($detail->apikey) && isset($detail->merchantcode)) {
                            if (in_array($payment, $addons->channel_payments)) {
                                $fee = $this->feepaymentgateway->row(array(
                                    'paymentgatewayid' => $paymentgateway->id,
                                    'paymentname' => $payment,
                                    "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                ));

                                if ($fee != null) {
                                    $feeamount = 0;
                                    if ($fee->feetype == 'Persentase') {
                                        $feeamount = $totalharga * $fee->fee / 100;
                                    } else if ($fee->feetype == 'Nominal') {
                                        $feeamount = $fee->fee;
                                    }
                                }

                                $paymentimage = $fee->paymentimage;
                            } else {
                                throw new Exception('Pembayaran tidak ditemukan');
                            }
                        } else {
                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                        }
                    } else if ($paymentgateway != null && $paymentgateway->vendor == 'Okeconnect' && $paymentgateway->addons != null) {

                        $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                        $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                        if (isset($addons->channel_payments)) {
                            if (in_array($payment, $addons->channel_payments) && isset(getOkeconnectPayments()[$payment])) {
                                $fee = $this->feepaymentgateway->row(array(
                                    'paymentgatewayid' => $paymentgateway->id,
                                    'paymentname' => $payment,
                                    "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                ));

                                if ($fee != null) {
                                    $feeamount = 0;
                                    if ($fee->feetype == 'Persentase') {
                                        $feeamount = $totalharga * $fee->fee / 100;
                                    } else if ($fee->feetype == 'Nominal') {
                                        $feeamount = $fee->fee;
                                    }
                                }

                                $paymentimage = $fee->paymentimage;
                            } else {
                                throw new Exception('Pembayaran tidak ditemukan');
                            }
                        } else {
                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                        }
                    } else {
                        throw new Exception('Pembayaran tidak ditemukan');
                    }
                } else {
                    if (isset($json->type) && $json->type == 'Notification Handler') {
                        $userbuynotificationhandler = $this->userbuynotificationhandler->select('a.*, b.packagename')
                            ->join('msnotificationhandlerpackage b', 'b.id = a.notificationid')
                            ->get(array(
                                'a.userid' => $this->merchant->id,
                                'a.id' => $json->id,
                                'b.isactive' => 1,
                                "(a.isdisabled IS NULL OR a.isdisabled = '0') =" => true,
                            ));

                        if ($userbuynotificationhandler->num_rows() == 0) {
                            throw new Exception('Pembayaran tidak ditemukan');
                        }

                        $rowuserbuynotificationhandler = $userbuynotificationhandler->row();

                        if ($rowuserbuynotificationhandler->isdisabled == 1) {
                            throw new Exception('Pembayaran yang anda pilih tidak aktif');
                        } else if ($rowuserbuynotificationhandler->minnominal > $totalharga) {
                            throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($rowuserbuynotificationhandler->minnominal));
                        } else if ($rowuserbuynotificationhandler->maxnominal != 0 && $rowuserbuynotificationhandler->maxnominal < $totalharga) {
                            throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($rowuserbuynotificationhandler->maxnominal));
                        }

                        if ($rowuserbuynotificationhandler->isfee == 1) {
                            $feeamount = 0;
                            if ($rowuserbuynotificationhandler->feetype == 'Persentase') {
                                $feeamount = $totalharga * $rowuserbuynotificationhandler->nominalfee / 100;
                            } else if ($rowuserbuynotificationhandler->feetype == 'Nominal') {
                                $feeamount = $rowuserbuynotificationhandler->nominalfee;
                            }
                        }

                        $paymentimage = $rowuserbuynotificationhandler->paymentimage;
                    } else {
                        $paymentgateway = $this->mspaymentgateway->get(array(
                            'userid' => $this->merchant->id,
                            'type' => 'Payment Gateway',
                            "(isdisabled IS NULL OR isdisabled = 0) =" => true
                        ))->row();

                        if ($paymentgateway == null) {
                            throw new Exception('Pembayaran tidak ditemukan');
                        } else if ($paymentgateway->isdisabled == 1) {
                            throw new Exception('Pembayaran yang anda pilih tidak aktif');
                        }

                        if ($paymentgateway->minnominal > $totalharga) {
                            throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->minnominal));
                        } else if ($paymentgateway->maxnominal != 0 && $paymentgateway->maxnominal < $totalharga) {
                            throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->maxnominal));
                        }

                        if ($paymentgateway->vendor == 'PayDisini' && $paymentgateway->addons != null && isset($json->id)) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            $paydisini = new PayDisini($detail->apikey);
                            $paymentchannel = $paydisini->paymentChannel();

                            if (isset($paymentchannel->success) && $paymentchannel->success == true) {
                                $paymentchannel = $paymentchannel->data;

                                if (isset($addons->channel_payments)) {
                                    if (in_array($json->id, $addons->channel_payments) && in_array($json->id, array_column($paymentchannel, 'id'))) {
                                        $fee = $this->feepaymentgateway->row(array(
                                            'paymentgatewayid' => $paymentgateway->id,
                                            'paymentname' => $json->id,
                                            "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                        ));

                                        if ($fee != null) {
                                            $feeamount = 0;
                                            if ($fee->feetype == 'Persentase') {
                                                $feeamount = $totalharga * $fee->fee / 100;
                                            } else if ($fee->feetype == 'Nominal') {
                                                $feeamount = $fee->fee;
                                            }
                                        }

                                        $paymentimage = $fee->paymentimage;
                                    } else {
                                        throw new Exception('Pembayaran tidak ditemukan');
                                    }
                                } else {
                                    throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                }
                            } else {
                                throw new Exception('Pembayaran tidak ditemukan');
                            }
                        } else {
                            throw new Exception('Pembayaran tidak ditemukan');
                        }
                    }
                }
            } else {
                $paymentgateway = $this->mspaymentgateway->get(array(
                    'id' => $payment,
                    'userid' => $this->merchant->id,
                    "(isdisabled IS NULL OR isdisabled = 0) =" => true
                ));

                if ($paymentgateway->num_rows() == 0) {
                    throw new Exception('Pembayaran tidak ditemukan');
                }

                $rowpaymentgateway = $paymentgateway->row();

                if ($rowpaymentgateway->isdisabled == 1) {
                    throw new Exception('Pembayaran yang anda pilih tidak aktif');
                } else if ($rowpaymentgateway->minnominal > $totalharga) {
                    throw new Exception('Minimal Topup Rp' . IDR($rowpaymentgateway->minnominal));
                } else if ($rowpaymentgateway->maxnominal != 0 && $rowpaymentgateway->maxnominal < $totalharga) {
                    throw new Exception('Maksimal Topup Rp' . IDR($rowpaymentgateway->maxnominal));
                }

                $detail = json_decode(stringEncryption('decrypt', $rowpaymentgateway->detail));

                if ($rowpaymentgateway->feetype != null) {
                    $feeamount = 0;
                    if ($rowpaymentgateway->feetype == 'Persentase') {
                        $feeamount = $totalharga * $rowpaymentgateway->nominalfee / 100;
                    } else if ($rowpaymentgateway->feetype == 'Nominal') {
                        $feeamount = $rowpaymentgateway->nominalfee;
                    }
                }

                $paymentimage = $rowpaymentgateway->paymentimage;
            }
        } else {
            throw new Exception('Metode Pembayaran tidak ditemukan');
        }

        $data['product'] = $productRow;
        $data['additional'] = $additional;
        $data['target'] = $target;
        $data['qty'] = $qty;
        $data['totalharga'] = $totalharga;
        $data['totalhargaorigin'] = $totalhargaorigin;
        $data['feeamount'] = $feeamount;
        $data['paymenttype'] = $paymenttype;
        $data['payment'] = stringEncryption('encrypt', $payment);
        $data['phonenumber'] = $phonenumber;
        $data['email'] = $email;
        $data['paymentimage'] = $paymentimage;
        $data['smmplatform'] = $getdetailplatform->asseturl ?? null;

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => viewTemplate($this->merchant->id, "landing/smm/confirm_order", $data, true)
        ));
    }

    public function process_order_smm()
    {
        try {
            $this->db->trans_begin();

            if (getCurrentThemeConfiguration($this->merchant->id) != 'Fin-App' && getCurrentThemeConfiguration($this->merchant->id) != 'Sobat-Serverppob') {
                $this->db->trans_rollback();

                return show_404();
            }

            $phoneUtil = PhoneNumberUtil::getInstance();

            $product = getPost('product');
            $additional = getPost('additional');
            $target = getPost('target');
            $qty = getPost('qty');
            $pin = getPost('pin');
            $paymenttype = getPost('paymenttype');
            $payment = getPost('paymentmethod');
            $phonenumber = getPost('phonenumber');
            $email = getPost('email');

            if ($product == null) {
                throw new Exception('Produk wajib diisi');
            } else if ($target == null) {
                throw new Exception('Target wajib diisi');
            } else if ($qty == null) {
                throw new Exception('Jumlah wajib diisi');
            } else if (!is_numeric($qty)) {
                throw new Exception('Jumlah harus berupa angka');
            } else if ($phonenumber == null) {
                throw new Exception('Nomor Telepon wajib diisi');
            } else if ($paymenttype == null) {
                throw new Exception('Metode Pembayaran wajib diisi');
            } else if ($payment == null) {
                throw new Exception('Pembayaran wajib diisi');
            } else if ($email == null) {
                throw new Exception('Email wajib diisi');
            } else {
                try {
                    $idNumberProto = $phoneUtil->parse($phonenumber, 'ID');
                    $isValid = $phoneUtil->isValidNumber($idNumberProto);

                    if (!$isValid) {
                        throw new Exception('Nomor Telepon yang anda masukkan tidak valid');
                    }

                    $phonenumber = $phoneUtil->format($idNumberProto, PhoneNumberFormat::E164);
                    $phonenumber = substr($phonenumber, 1);
                } catch (NumberParseException $ex) {
                    throw new Exception('Nomor Telepon yang anda masukkan tidak valid');
                }
            }

            $payment = stringEncryption('decrypt', $payment);
            $product = stringEncryption('decrypt', $product);

            $currentName = $target;
            $currentPhone = $phonenumber;
            $CurrentEmail = $email ?? null;

            $insert = array();
            $insert['clientcode'] = generateTransactionNumber('SMM');
            $insert['userid'] = getCurrentIdUser();

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'SMM'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $where = array(
                'id' => $product,
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'SMM',
                'category !=' => null
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('SMM', $this->merchant->id);
                $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                $where['vendorid'] = null;
            } else {
                $where["((vendorid IS NOT NULL vendorenabled = 1) OR vendor IS NULL) ="] = true;
            }

            $getproduct = $this->msproduct->where_not_in('category', $disabled_category)
                ->get($where);

            if ($getproduct->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $productRow = $getproduct->row();

            if ($productRow->status != 1) {
                throw new Exception('Produk sedang gangguan');
            }

            if ($productRow->minorder > $qty) {
                throw new Exception("Jumlah minimal order adalah " . IDR($productRow->minorder));
            }

            if ($productRow->maxorder < $qty) {
                throw new Exception("Jumlah maksimal order adalah " . IDR($productRow->maxorder));
            }

            if (isLogin()) {
                if ($pin == null) {
                    throw new Exception('PIN Transaksi wajib diisi');
                }

                $currentuser = getCurrentUser(null, true);
                $currentName = $currentuser->name;
                $CurrentEmail = $currentuser->email;

                if (stringEncryption('encrypt', $pin) != $currentuser->pin) {
                    throw new Exception('PIN Transaksi yang anda masukkan salah');
                }

                if ($currentuser->roleid == null) {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'isdefault' => '1'
                    ))->row();
                } else {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'id' => $currentuser->roleid
                    ))->row();
                }

                if ($getrole != null) {
                    if ($getrole->discounttype == 'Simple') {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->get(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            'servicetype' => 'SMM'
                        ))->result();
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = 'Simple';
                    $discount = 0;
                }

                $getpending = $this->trorder->total(array(
                    'userid' => getCurrentIdUser(),
                    'serviceid' => $productRow->id,
                    'status' => 'pending',
                    'target' => $target
                ));

                if ($getpending > 0) {
                    throw new Exception('Anda memiliki transaksi yang masih dalam proses');
                }
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();

                if ($getrole != null) {
                    if ($getrole->discounttype == 'Simple') {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->get(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            'servicetype' => 'SMM'
                        ))->result();
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = 'Simple';
                    $discount = 0;
                }

                $insert['clientip'] = get_client_ip();

                $getpending = $this->trorder->total(array(
                    'clientip' => get_client_ip(),
                    'serviceid' => $productRow->id,
                    'status' => 'pending',
                    'target' => $target
                ));

                if ($getpending > 0) {
                    throw new Exception('Anda memiliki transaksi yang masih dalam proses');
                }
            }

            $fixproductprice = 0;

            if ($getrole->discounttype == 'Simple') {
                $fixproductprice = $productRow->price - $discount;
            } else {
                $found = false;

                foreach ($discount as $val) {
                    if ($found) continue;

                    if ($val->startrange <= $productRow->price && $val->endrange >= $productRow->price) {
                        if ($val->discounttype == 'Persentase') {
                            $fixproductprice = $productRow->price - ($productRow->price * $val->nominal / 100);

                            $found = true;
                        } else {
                            $fixproductprice = $productRow->price - $val->nominal;

                            $found = true;
                        }
                    }
                }

                if ($found == false) {
                    $fixproductprice = $productRow->price;
                }
            }

            $totalharga = round(($fixproductprice / 1000) * $qty);
            $totalhargaorigin = ($productRow->price / 1000) * $qty;

            $potonganprofit = $totalhargaorigin - $totalharga;

            $totalhargabyvendor = ($productRow->vendorprice / 1000) * $qty;
            $profit = (($productRow->profit / 1000) * $qty) - $potonganprofit;

            if ($paymenttype == 'Saldo' && isLogin()) {
                if ($currentuser->balance < $totalharga) {
                    throw new Exception('Saldo anda tidak mencukupi');
                }

                $insert['status_payment'] = 'sukses';
            } else if ($paymenttype == 'Manual') {
                $insert['status_payment'] = 'pending';

                $get = $this->mspaymentmethod->get(array(
                    'userid' => $this->merchant->id,
                    'id' => $payment,
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Pembayaran tidak ditemukan');
                }

                $row = $get->row();

                if ($row->isdisabled == 1) {
                    throw new Exception('Pembayaran yang anda pilih tidak aktif');
                } elseif ($row->minnominal > $totalharga) {
                    throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($row->minnominal));
                } else if ($row->maxnominal != 0 && $row->maxnominal < $totalharga) {
                    throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($row->maxnominal));
                } else if ($row->isunique == 1) {
                    if ($this->merchant->uniquenominal_start != null && $this->merchant->uniquenominal_end != null && $this->merchant->uniquenominal_start != 0 && $this->merchant->uniquenominal_end != 0) {
                        $unique = rand($this->merchant->uniquenominal_start, $this->merchant->uniquenominal_end);
                    } else {
                        $unique = rand(100, 999);
                    }

                    $totalharga = $totalharga + $unique;

                    if ($row->uniqueadmin == 1) {
                        $insert['uniqueadmin'] = $unique;
                    }
                }

                $insert['payment'] = $row->paymentmethod;
                $insert['price'] = $totalharga;
                $insert['paymenttype'] = 'Manual';
                $insert['paymentmethodid'] = $row->id;

                if ($row->feetype != null) {
                    $feeamount = 0;

                    if ($row->feetype == 'Persentase') {
                        $feeamount = $totalharga * (((int) $row->nominalfee) / 100);
                    } else if ($row->feetype == 'Nominal') {
                        $feeamount = $row->nominalfee;
                    }

                    $insert['fee'] = $feeamount;
                    $insert['price'] = $insert['price'] + $feeamount;
                }
            } else if ($paymenttype == 'Otomatis') {
                $insert['paymenttype'] = 'Otomatis';
                $insert['status_payment'] = 'pending';

                if (!is_numeric($payment)) {
                    $json = json_decode($payment);

                    if (!is_object($json) && !isset($json->type)) {
                        $paymentgateway = $this->mspaymentgateway->get(array(
                            'userid' => $this->merchant->id,
                            'type' => 'Payment Gateway',
                            "(isdisabled IS NULL OR isdisabled = 0) =" => true
                        ))->row();

                        if ($paymentgateway == null) {
                            throw new Exception('Pembayaran tidak ditemukan');
                        } else if ($paymentgateway->isdisabled == 1) {
                            throw new Exception('Pembayaran yang anda pilih tidak aktif');
                        }

                        if ($paymentgateway->minnominal > $totalharga) {
                            throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->minnominal));
                        } else if ($paymentgateway->maxnominal != 0 && $paymentgateway->maxnominal < $totalharga) {
                            throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->maxnominal));
                        }

                        if ($paymentgateway != null && $paymentgateway->vendor == 'Midtrans' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->enabled_payments)) {
                                if (in_array($payment, $addons->enabled_payments) && isset(getMidtransPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $totalharga * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        $insert['fee'] = $feeamount;
                                        $totalharga = $totalharga + $feeamount;
                                    }

                                    Config::$serverKey = $detail->serverkey;
                                    Config::$clientKey = $detail->clientkey;
                                    Config::$isProduction = ENVIRONMENT == 'production' ? true : false;

                                    $params = array(
                                        'transaction_details' => array(
                                            'order_id' => $insert['clientcode'],
                                            'gross_amount' => $totalharga,
                                        ),
                                        'enabled_payments' => array(
                                            $payment
                                        )
                                    );

                                    try {
                                        $snapToken = Snap::getSnapToken($params);
                                    } catch (Exception $snapex) {
                                        if ($snapex->getMessage() != null) {
                                            log_message_user('error', "[MIDTRANS TOPUP] Response: " . $snapex->getMessage(), $this->merchant->id);
                                        }

                                        throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                    }

                                    $insert['payment'] = getMidtransPayments()[$payment];
                                    $insert['price'] = $totalharga;
                                    $insert['gatewayvendor'] = $paymentgateway->vendor;
                                    $insert['snaptoken'] = $snapToken;
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Tripay' && $paymentgateway->addons != null) {
                            if ($payment == 'OVO') {
                                if ($currentPhone == null) {
                                    throw new Exception('Nomor Handphone OVO tidak boleh kosong');
                                } else if (!is_numeric($currentPhone)) {
                                    throw new Exception('Nomor Handphone OVO tidak valid');
                                } else {
                                    $insert['phonenumber_order'] = $currentPhone;
                                }
                            }

                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getTripayPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $totalharga * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        $insert['fee'] = $feeamount;
                                        $totalharga = $totalharga + $feeamount;
                                    }

                                    $tripay = new Tripay($detail->merchantcode, $detail->apikey, $detail->privatekey);
                                    $request = $tripay->requestTransaction($payment, $insert['clientcode'], $totalharga, $currentName, $CurrentEmail, $currentPhone, "Transaksi", 1, base_url('history?userid=' . $this->userid));

                                    if (isset($request->success) && $request->success) {
                                        $insert['payment'] = getTripayPayments()[$payment];
                                        $insert['price'] = $totalharga;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;
                                        $insert['checkouturl'] = $request->data->checkout_url;
                                        $insert['servercode_payment'] = $request->data->reference;
                                        $insert['jsonresponse_payment'] = json_encode($request);
                                    } else {
                                        if ($request != null) {
                                            log_message_user('error', '[TRIPAY TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                        }

                                        if (isset($request->message) && $request->message) {
                                            throw new Exception($request->message);
                                        } else {
                                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                        }
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'iPaymu' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getIpaymuPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $totalharga * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        $insert['fee'] = $feeamount;
                                        $totalharga = $totalharga + $feeamount;
                                    }

                                    $ipaymu = new iPaymu($detail->apikey, $detail->virtualaccount, ENVIRONMENT == 'production');
                                    $ipaymu->setURL([
                                        'ureturn' => base_url('landing'),
                                        'unotify' => base_url('callback/ipaymu')
                                    ]);

                                    $ipaymu->setBuyer([
                                        'name' => $currentName,
                                        'phone' => $currentPhone ?? '*************',
                                        'email' => $CurrentEmail
                                    ]);

                                    $ipaymu->setReferenceId($insert['clientcode']);
                                    $ipaymu->setPaymentChannel($payment);
                                    $ipaymu->setPaymentMethod(getIpaymuMethod()[$payment]);

                                    $carts = [];
                                    $carts = $ipaymu->add($insert['clientcode'], 'Transaksi', $totalharga, 1, "Transaksi senilai Rp " . IDR($totalharga), 0, 0, 0, 0);

                                    $ipaymu->addCart($carts);

                                    $request = $ipaymu->directPayment();

                                    if (isset($request['Status']) && $request['Status'] == 200) {
                                        $paymentNo = $request['Data']['PaymentNo'] ?? '- Unknown -';
                                        $paymentName = $request['Data']['PaymentName'] ?? '- Unknown -';
                                        $paymentChannel = $request['Data']['Channel'] ?? '- Unknown -';

                                        $insert['payment'] = getIpaymuPayments()[$payment];
                                        $insert['price'] = $totalharga;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;
                                        $insert['servercode_payment'] = $request['Data']['TransactionId'];
                                        $insert['jsonresponse_payment'] = json_encode($request);

                                        if ($payment != 'qris') {
                                            $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($totalharga) . ",- Ke Rekening: $paymentChannel, no. $paymentNo a.n $paymentName. Batas waktu transfer 24 jam";
                                        } else {
                                            $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($totalharga) . ",- Ke Rekening: $paymentChannel a.n $paymentName. Batas waktu transfer 24 jam";
                                            $insert['isqr'] = 1;
                                        }
                                    } else {
                                        if ($request != null) {
                                            log_message_user('error', '[IPAYMU TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                        }

                                        throw new Exception($request['Message'] ?? 'Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Duitku' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments) && isset($addons->channel_payments_name) && isset($detail->apikey) && isset($detail->merchantcode)) {
                                if (in_array($payment, $addons->channel_payments)) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $totalharga * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        $insert['fee'] = $feeamount;
                                        $totalharga = $totalharga + $feeamount;
                                    }

                                    $params = array(
                                        'paymentAmount' => $totalharga,
                                        'paymentMethod' => $payment,
                                        'merchantOrderId' => $insert['clientcode'],
                                        'productDetails' => 'Transkasi',
                                        'customerVaName' => $this->merchant->companyname,
                                        'email' => $CurrentEmail,
                                        'callbackUrl' => base_url('callback/duitku'),
                                        'returnUrl' => base_url('history?userid=' . $this->userid),
                                    );

                                    $apikey = $detail->apikey;
                                    $merchantcode = $detail->merchantcode;

                                    $duitku_config = new \Duitku\Config($apikey, $merchantcode, ENVIRONMENT == 'development', true, false);

                                    $paymentMethod = Api::getPaymentMethod($totalharga, $duitku_config);
                                    $paymentMethod = json_decode($paymentMethod);

                                    $paymentName = null;
                                    foreach ($paymentMethod->paymentFee as $key => $value) {
                                        if ($value->paymentMethod == $payment) {
                                            $paymentName = $value->paymentName;
                                        }
                                    }

                                    if ($paymentName == null) {
                                        throw new Exception('Pembayaran tidak ditemukan');
                                    }

                                    $create = Api::createInvoice($params, $duitku_config);
                                    $create = json_decode($create);

                                    if (isset($create->statusCode) && $create->statusCode == '00') {
                                        $insert['payment'] = $paymentName;
                                        $insert['price'] = $totalharga;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;
                                        $insert['checkouturl'] = $create->paymentUrl;
                                        $insert['servercode_payment'] = $create->reference;
                                        $insert['jsonresponse_payment'] = json_encode($create);
                                    } else {
                                        if ($create != null) {
                                            log_message_user('error', '[DUITKU TOPUP] Response: ' . json_encode($create), $this->merchant->id);
                                        }

                                        throw new Exception($create->statusMessage ?? 'Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Okeconnect' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getOkeconnectPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;
                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $totalharga * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        $insert['fee'] = $feeamount;
                                        $totalharga = $totalharga + $feeamount;
                                    }

                                    if ($payment == 'MANDIRI' || $payment == 'MUAMALAT' || $payment == 'BRI' || $payment == 'BNI' || $payment == 'PERMATA' || $payment == 'BSI' || $payment == 'CIMB' || $payment == 'NEO' || $payment == 'DANAMON' || $payment == 'MAYBANK') {
                                        $type = 'VA';
                                    } else if ($payment == 'DANA' || $payment == 'OVO' || $payment == 'Gopay' || $payment == 'Shopee' || $payment == 'Link Aja' || $payment == 'All E-Wallet' || $payment == 'Mobile Bangking') {
                                        $type = 'EWALLET';
                                    } else if ($payment == 'ALFAMART' || $payment == 'INDOMARET') {
                                        $type = 'RETAIL';
                                    }

                                    $okeconnect = new Okeconnect($detail->merchantcode, $detail->apikey);
                                    $request = $okeconnect->requestTransaction($totalharga, $insert['clientcode'], 'Pembelian produk ' . $productRow->productname, $CurrentEmail, $payment, $currentPhone, null, base_url('history?userid=' . $this->userid), $type);

                                    if (($type == 'VA' && isset($request->status) && $request->status == TRUE) || ($type == 'RETAIL' && isset($request->success) && $request->success == TRUE && isset($request->response->status) && $request->response->status == 'success')) {
                                        $insert['payment'] = getOkeconnectPayments()[$payment];
                                        $insert['price'] = $totalharga;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;

                                        if ($type == 'VA') {
                                            $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($totalharga) . ",- Ke Rekening Virual Account: " . getOkeconnectPayments()[$payment] . ", no. " . $request->code->$payment . " Batas waktu transfer 24 jam";
                                            $insert['servercode_payment'] = $request->reff;
                                        } else if ($type == 'RETAIL') {
                                            $paymentresponse = strtolower($payment);
                                            $insert['note_payment'] = "Silahkan datang ke kasir " . getOkeconnectPayments()[$payment] . " dan bayar Okeconnect Rp " . IDR($totalharga) . ",dengan kode pembayaran " . $request->response->$paymentresponse->code . " Batas waktu transfer 24 jam";
                                            $insert['servercode_payment'] = $request->response->$paymentresponse->code;
                                        }

                                        $insert['jsonresponse_payment'] = json_encode($request);
                                    } else {
                                        if ($request != null) {
                                            log_message_user('error', '[Okeconnect TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                        }

                                        if (isset($request->message) && $request->message) {
                                            throw new Exception($request->message);
                                        } else {
                                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                        }
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else {
                            throw new Exception('Pembayaran tidak ditemukan');
                        }
                    } else {
                        if (isset($json->type) && $json->type == 'Notification Handler') {
                            $userbuynotificationhandler = $this->userbuynotificationhandler->select('a.*, b.packagename')
                                ->join('msnotificationhandlerpackage b', 'b.id = a.notificationid')
                                ->get(array(
                                    'a.userid' => $this->merchant->id,
                                    'a.id' => $json->id,
                                    'b.isactive' => 1,
                                    "(a.isdisabled IS NULL OR a.isdisabled = '0') =" => true,
                                ));

                            if ($userbuynotificationhandler->num_rows() == 0) {
                                throw new Exception('Pembayaran tidak ditemukan');
                            }

                            $rowuserbuynotificationhandler = $userbuynotificationhandler->row();

                            if ($rowuserbuynotificationhandler->isdisabled == 1) {
                                throw new Exception('Pembayaran yang anda pilih tidak aktif');
                            } else if ($rowuserbuynotificationhandler->minnominal > $totalharga) {
                                throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($rowuserbuynotificationhandler->minnominal));
                            } else if ($rowuserbuynotificationhandler->maxnominal != 0 && $rowuserbuynotificationhandler->maxnominal < $totalharga) {
                                throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($rowuserbuynotificationhandler->maxnominal));
                            }

                            if ($this->merchant->uniquenominal_start != null && $this->merchant->uniquenominal_end != null && $this->merchant->uniquenominal_start != 0 && $this->merchant->uniquenominal_end != 0) {
                                $unique = rand($this->merchant->uniquenominal_start, $this->merchant->uniquenominal_end);
                            } else {
                                $unique = rand(100, 999);
                            }

                            $totalharga = $totalharga + $unique;

                            $insert['payment'] = $rowuserbuynotificationhandler->packagename;
                            $insert['price'] = $totalharga;
                            $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($totalharga) . ",- Ke Rekening: $rowuserbuynotificationhandler->packagename, no. $rowuserbuynotificationhandler->accountnumber a.n. $rowuserbuynotificationhandler->accountname. Batas waktu transfer 24 jam";
                            $insert['gatewayvendor'] = 'Notification Handler Services';
                            $insert['paymentmethodid'] = $rowuserbuynotificationhandler->id;
                            $insert['uniqueadmin'] = $unique;

                            if ($rowuserbuynotificationhandler->isfee == 1) {
                                $feeamount = 0;
                                if ($rowuserbuynotificationhandler->feetype == 'Persentase') {
                                    $feeamount = $totalharga * $rowuserbuynotificationhandler->nominalfee / 100;
                                } else if ($rowuserbuynotificationhandler->feetype == 'Nominal') {
                                    $feeamount = $rowuserbuynotificationhandler->nominalfee;
                                }

                                $insert['fee'] = $feeamount;
                                $insert['price'] = $totalharga + $feeamount;
                                $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($insert['price']) . ",- Ke Rekening: $rowuserbuynotificationhandler->packagename, no. $rowuserbuynotificationhandler->accountnumber a.n. $rowuserbuynotificationhandler->accountname. Batas waktu transfer 24 jam";
                            }
                        } else {
                            $paymentgateway = $this->mspaymentgateway->get(array(
                                'userid' => $this->merchant->id,
                                'type' => 'Payment Gateway',
                                "(isdisabled IS NULL OR isdisabled = 0) =" => true
                            ))->row();

                            if ($paymentgateway == null) {
                                throw new Exception('Pembayaran tidak ditemukan');
                            } else if ($paymentgateway->isdisabled == 1) {
                                throw new Exception('Pembayaran yang anda pilih tidak aktif');
                            }

                            if ($paymentgateway->minnominal > $totalharga) {
                                throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->minnominal));
                            } else if ($paymentgateway->maxnominal != 0 && $paymentgateway->maxnominal < $totalharga) {
                                throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($paymentgateway->maxnominal));
                            }

                            if ($paymentgateway->vendor == 'PayDisini' && $paymentgateway->addons != null && isset($json->id)) {
                                $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                                $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                                $paydisini = new PayDisini($detail->apikey);
                                $paymentchannel = $paydisini->paymentChannel();

                                if (isset($paymentchannel->success) && $paymentchannel->success == true) {
                                    $paymentchannel = $paymentchannel->data;

                                    if (isset($addons->channel_payments)) {
                                        if (in_array($json->id, $addons->channel_payments) && in_array($json->id, array_column($paymentchannel, 'id'))) {
                                            $fee = $this->feepaymentgateway->row(array(
                                                'paymentgatewayid' => $paymentgateway->id,
                                                'paymentname' => $json->id,
                                                "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                            ));

                                            if ($fee != null) {
                                                $feeamount = 0;
                                                if ($fee->feetype == 'Persentase') {
                                                    $feeamount = $totalharga * $fee->fee / 100;
                                                } else if ($fee->feetype == 'Nominal') {
                                                    $feeamount = $fee->fee;
                                                }

                                                $insert['fee'] = $feeamount;
                                                $totalharga = $totalharga + $feeamount;
                                            }

                                            $totalharga = round($totalharga);
                                            $insert['paydisinicode'] = generateUniqueCodePayDisini('SMM-');

                                            $request = $paydisini->requestTransaction($json->id, $insert['paydisinicode'], $totalharga, 'Pembelian Produk ' . $productRow->productname, $currentPhone, $json->type);

                                            if (isset($request->success) && $request->success == TRUE) {
                                                $insert['payment'] = $addons->alias_payments[array_keys($addons->channel_payments, $json->id)[0] ?? ""] ?? $addons->channel_payments_name[array_search($json->id, $addons->channel_payments)];
                                                $insert['price'] = $totalharga;
                                                $insert['gatewayvendor'] = $paymentgateway->vendor;

                                                if ($json->type == 'VA') {
                                                    $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($totalharga) . ",- Ke Rekening: " . ($addons->alias_payments[array_keys($addons->channel_payments, $json->id)[0] ?? ""] ?? $addons->channel_payments_name[array_search($json->id, $addons->channel_payments)]) . ", no. " . $request->data->virtual_account . " Batas waktu transfer 3 jam hingga pukul" . DateFormat($request->data->expired, 'd F Y H:i:s');
                                                } else if ($json->type == 'Retail') {
                                                    $insert['note_payment'] = "Bayar di " . ($addons->alias_payments[array_keys($addons->channel_payments, $json->id)[0] ?? ""] ?? $addons->channel_payments_name[array_search($json->id, $addons->channel_payments)]) . " sebesar Rp " . IDR($totalharga) . ",- dan beritahu kasir kode pembayaran: " . $request->data->payment_code . " Batas waktu transfer 3 jam hingga pukul" . DateFormat($request->data->expired, 'd F Y H:i:s');
                                                } else {
                                                    $insert['note_payment'] = $request->data->note;
                                                }

                                                if ($json->type == 'QRIS') {
                                                    $insert['isqr'] = 1;
                                                }

                                                $insert['servercode_payment'] = $request->data->pay_id;
                                                $insert['checkouturl'] = $request->data->checkout_url;
                                                $insert['jsonresponse_payment'] = json_encode($request);
                                            } else {
                                                if ($request != null) {
                                                    log_message_user('error', '[PayDisini TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                                }

                                                if (isset($request->msg) && $request->msg) {
                                                    throw new Exception($request->msg);
                                                } else {
                                                    throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                                }
                                            }
                                        } else {
                                            throw new Exception('Pembayaran tidak ditemukan');
                                        }
                                    } else {
                                        throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                }
                            } else {
                                throw new Exception('Pembayaran tidak ditemukan');
                            }
                        }
                    }
                } else {
                    $paymentgateway = $this->mspaymentgateway->get(array(
                        'id' => $payment,
                        'userid' => $this->merchant->id,
                        "(isdisabled IS NULL OR isdisabled = 0) =" => true
                    ));

                    if ($paymentgateway->num_rows() == 0) {
                        throw new Exception('Pembayaran tidak ditemukan');
                    }

                    $rowpaymentgateway = $paymentgateway->row();

                    if ($rowpaymentgateway->isdisabled == 1) {
                        throw new Exception('Pembayaran yang anda pilih tidak aktif');
                    } else if ($rowpaymentgateway->minnominal > $totalharga) {
                        throw new Exception('Minimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($rowpaymentgateway->minnominal));
                    } else if ($rowpaymentgateway->maxnominal != 0 && $rowpaymentgateway->maxnominal < $totalharga) {
                        throw new Exception('Maksimal Nominal Menggunakan Methode Pembayaran ini adalah Rp' . IDR($rowpaymentgateway->maxnominal));
                    }

                    $detail = json_decode(stringEncryption('decrypt', $rowpaymentgateway->detail));

                    $insert['payment'] = $rowpaymentgateway->type;

                    if ($this->merchant->uniquenominal_start != null && $this->merchant->uniquenominal_end != null && $this->merchant->uniquenominal_start != 0 && $this->merchant->uniquenominal_end != 0) {
                        $unique = rand($this->merchant->uniquenominal_start, $this->merchant->uniquenominal_end);

                        $insert['price'] = $totalharga + $unique;
                    } else {
                        $insert['price'] = $totalharga + rand(100, 999);
                    }

                    if ($rowpaymentgateway->feetype != null) {
                        $feeamount = 0;
                        if ($rowpaymentgateway->feetype == 'Persentase') {
                            $feeamount = $totalharga * $rowpaymentgateway->nominalfee / 100;
                        } else if ($rowpaymentgateway->feetype == 'Nominal') {
                            $feeamount = $rowpaymentgateway->nominalfee;
                        }

                        $insert['fee'] = $feeamount;
                        $insert['price'] = $totalharga + $feeamount;
                    }

                    if ($rowpaymentgateway->vendor == 'BCA') {
                        $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($insert['price']) . ",- Ke Rekening: BCA, no. $detail->accountnumber a.n. $detail->accountname. Batas waktu transfer 24 jam";
                    } else if ($rowpaymentgateway->vendor == 'GOPAY') {
                        $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($insert['price']) . ",- Ke Rekening: GOPAY, no. $detail->phonenumber. Batas waktu transfer 24 jam";
                    } else if ($rowpaymentgateway->vendor == 'OVO') {
                        $insert['note_payment'] = "Silahkan transfer sebesar Rp " . IDR($insert['price']) . ",- Ke Rekening: OVO, no. $detail->phonenumber. Batas waktu transfer 24 jam";
                    }
                }
            } else {
                throw new Exception('Pembayaran tidak ditemukan');
            }

            $queuetransaction = false;

            if ($this->merchant->multivendor != 1) {
                $apikeys = getCurrentAPIKeys('SMM', $this->merchant->id);
                if ($apikeys->balance < $totalhargabyvendor && ENVIRONMENT == 'production') {
                    $queuetransaction = true;
                }
            } else {
                if (getCurrentBalanceVendor($this->merchant->id, $productRow->vendorid) < $totalharga && ENVIRONMENT == 'production') {
                    $queuetransaction = true;
                }
            }

            $insert['serviceid'] = $productRow->id;
            $insert['target'] = $target;
            $insert['qty'] = $qty;
            $insert['profit'] = $profit;

            if (isLogin()) {
                $insert['currentsaldo'] = $currentuser->balance;
            }

            $insert['status'] = 'pending';
            $insert['type'] = 'SMM';
            $insert['queuetransaction'] = $queuetransaction ? 1 : 0;
            $insert['category_apikey'] = $productRow->category_apikey;
            $insert['subcategory_apikey'] = $productRow->subcategory_apikey;
            $insert['orderplatform'] = 'web';
            $insert['productcode'] = $productRow->code;
            $insert['productname_order'] = $productRow->productname;
            $insert['vendor'] = $productRow->vendor;
            $insert['email_order'] = $CurrentEmail;
            $insert['phonenumber_order'] = $currentPhone;
            $insert['merchantid_order'] = $this->merchant->id;
            $insert['vendorid'] = $productRow->vendorid;

            if ($productRow->iscustom == 1) {
                $insert['additional'] = $additional;
            }

            $this->trorder->insert($insert);
            $insert_id = $this->db->insert_id();

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Transaksi gagal');
            }

            pullTransaction($this->merchant->id);

            if (isLogin() && $paymenttype == 'Saldo') {
                $inserthistorybalance = array();
                $inserthistorybalance['userid'] = getCurrentIdUser();
                $inserthistorybalance['type'] = 'OUT';
                $inserthistorybalance['nominal'] = $totalharga;
                $inserthistorybalance['currentbalance'] = $currentuser->balance;
                $inserthistorybalance['orderid'] = $insert_id;
                $inserthistorybalance['createdby'] = getCurrentIdUser();
                $inserthistorybalance['createddate'] = getCurrentDate();

                $this->historybalance->insert($inserthistorybalance);

                $update = array();
                $update['balance'] = $currentuser->balance - $totalharga;

                $this->msusers->update(array(
                    'id' => getCurrentIdUser()
                ), $update);

                if ($productRow->code != null) {
                    if ($queuetransaction == false && ENVIRONMENT == 'production') {
                        if ($this->merchant->multivendor != 1) {
                            if ($vendor == 'BuzzerPanel') {
                                $buzzerpanel = new BuzzerPanel(stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                                $order = $buzzerpanel->order($productRow->code, $target, $qty);

                                if (isset($order->status) && $order->status) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);

                                    $this->trorder->update(array(
                                        'id' => $insert_id
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($order != null) {
                                        log_message_user('error', '[BUZZERPANEL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                    }

                                    throw new Exception($order->data->msg);
                                }
                            } else if ($vendor == 'MedanPedia') {
                                $medanpedia = new MedanPedia(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                                $order = $medanpedia->order($productRow->code, $target, $qty, $productRow->iscustom == 1 ? $additional : '');

                                if ($order->status) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);

                                    $this->trorder->update(array(
                                        'id' => $insert_id
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($order != null) {
                                        log_message_user('error', '[MEDANPEDIA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                    }

                                    throw new Exception($order->data);
                                }
                            } else if ($vendor == 'IrvanKede') {
                                if ($productRow->iscustom == null) {
                                    $additional = '';
                                }

                                $irvankede = new IrvanKede(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                                $order = $irvankede->order(array(
                                    'service' => $productRow->code,
                                    'target' => $target,
                                    'quantity' => $qty,
                                    'custom_comments' => $additional,
                                    'custom_link' => $additional
                                ));

                                if ($order->status) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);

                                    $this->trorder->update(array(
                                        'id' => $insert_id
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($order != null) {
                                        log_message_user('error', '[IRVANKEDE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                    }

                                    throw new Exception($order->data);
                                }
                            } else if ($vendor == 'DailyPanel') {
                                $dailypanel = new DailyPanel(stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                                $order = $dailypanel->order($productRow->code, $target, $qty, $productRow->iscustom == 1 ? $additional : '');

                                if (isset($order->success) && $order->success) {
                                    $update = array();
                                    $update['servercode'] = $order->msg->order_id;
                                    $update['jsonresponse'] = json_encode($order);

                                    $this->trorder->update(array(
                                        'id' => $insert_id
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($order != null) {
                                        log_message_user('error', '[DAILYPANEL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                    }

                                    throw new Exception($order->msg->error);
                                }
                            } else if ($vendor == 'WStore') {
                                $wstore = new WStore(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                                $order = $wstore->order($productRow->code, $target, $qty);

                                if (isset($order->response) && $order->response) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);

                                    $this->trorder->update(array(
                                        'id' => $insert_id
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($order != null) {
                                        log_message_user('error', '[WSTORE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                    }

                                    throw new Exception($order->data->msg);
                                }
                            } else if ($vendor == 'UNDRCTRL') {
                                $undrctrl = new UNDRCTRL(stringEncryption('decrypt', $apikeys->apikey));
                                $order = $undrctrl->order($productRow->code, $target, $qty);

                                if (isset($order->order)) {
                                    $update = array();
                                    $update['servercode'] = $order->order;
                                    $update['jsonresponse'] = json_encode($order);

                                    $this->trorder->update(array(
                                        'id' => $insert_id
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($order != null) {
                                        log_message_user('error', '[UNDRCTRL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                    }

                                    throw new Exception($order->msg);
                                }
                            } else if ($vendor == 'SosmedOnline') {
                                $sosmedonline = new SosmedOnline(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                                $order = $sosmedonline->order($productRow->code, $target, $qty, $productRow->iscustom == 1 ? $additional : '');

                                if (isset($order->status) && $order->status) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);

                                    $this->trorder->update(array(
                                        'id' => $insert_id
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($order != null) {
                                        log_message_user('error', '[SOSMEDONLINE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                    }

                                    throw new Exception('Gagal melakukan pembelian');
                                }
                            } else if ($vendor == 'SosmedOnlineVIP') {
                                $sosmedonlinevip = new SosmedOnlineVIP(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                                $order = $sosmedonlinevip->order($productRow->code, $target, $qty, $productRow->iscustom == 1 ? $additional : '');

                                if (isset($order->status) && $order->status) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);

                                    $this->trorder->update(array(
                                        'id' => $insert_id
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($order != null) {
                                        log_message_user('error', '[SOSMEDONLINEVIP ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                    }

                                    throw new Exception('Gagal melakukan pembelian');
                                }
                            } else if ($vendor == 'DjuraganSosmed') {
                                $djuragansosmed = new DjuraganSosmed(stringEncryption('decrypt', $apikeys->apikey));
                                $order = $djuragansosmed->order($productRow->code, $target, $qty);

                                if (isset($order->order)) {
                                    $update = array();
                                    $update['servercode'] = $order->order;
                                    $update['jsonresponse'] = json_encode($order);

                                    $this->trorder->update(array(
                                        'id' => $insert_id
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($order != null) {
                                        log_message_user('error', '[DJURAGANSOSMED ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                    }

                                    throw new Exception('Gagal melakukan pembelian');
                                }
                            } else if ($vendor == 'SMMRaja') {
                                $smmraja = new SMMRaja(stringEncryption('decrypt', $apikeys->apikey));
                                $order = $smmraja->order($productRow->code, $target, $qty);

                                if (isset($order->order)) {
                                    $update = array();
                                    $update['servercode'] = $order->order;
                                    $update['jsonresponse'] = json_encode($order);

                                    $this->trorder->update(array(
                                        'id' => $insert_id
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($order != null) {
                                        log_message_user('error', '[SMMRAJA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                    }

                                    throw new Exception('Gagal melakukan pembelian');
                                }
                            } else if ($vendor == 'SMMIllusion') {
                                $smmillusion = new SMMIllusion(stringEncryption('decrypt', $apikeys->apikey));
                                $order = $smmillusion->order($productRow->code, $target, $qty);

                                if (isset($order->order)) {
                                    $update = array();
                                    $update['servercode'] = $order->order;
                                    $update['jsonresponse'] = json_encode($order);

                                    $this->trorder->update(array(
                                        'id' => $insert_id
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($order != null) {
                                        log_message_user('error', '[SMMILLUSION ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                    }

                                    throw new Exception('Gagal melakukan pembelian');
                                }
                            } else if ($vendor == 'V1Pedia') {
                                $v1pedia = new V1Pedia(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                                $order = $v1pedia->order($productRow->code, $target, $qty);

                                if (isset($order->response) && $order->response) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);

                                    $this->trorder->update(array(
                                        'id' => $insert_id
                                    ), $update);

                                    $this->db->trans_commit();

                                    $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

                                    return JSONResponse(array(
                                        'RESULT' => 'OK',
                                        'MESSAGE' => 'Pembelian berhasil',
                                        'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                    ));
                                } else {
                                    if ($order != null) {
                                        log_message_user('error', '[V1PEDIA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                    }

                                    throw new Exception($order->data->msg);
                                }
                            }
                        } else {
                            $order = $this->msvendordetail->select('a.*, b.default_config')
                                ->join('msvendor b', 'b.id = a.vendorid')
                                ->get(array(
                                    'a.vendorid' => $productRow->vendorid,
                                    'a.apitype' => 'Order'
                                ));

                            if ($order->num_rows() == 0) {
                                throw new Exception('Konfigurasi vendor tidak ditemukan');
                            }

                            $vendor_order = $order->row();

                            if ($vendor_order->default_config == null) {
                                throw new Exception('Konfigurasi vendor tidak ditemukan');
                            }

                            $response_indicator = json_decode($vendor_order->response_indicator);
                            $response_setting = json_decode($vendor_order->response_setting);

                            $dynamicvendor = new DynamicVendor($productRow->vendorid, json_decode($vendor_order->default_config, true));
                            $order = $dynamicvendor->order($insert_id);

                            if (
                                ($response_indicator->index == null && isset($order[$response_indicator->key]) && (
                                    $response_indicator->datatype == 'string' && $order[$response_indicator->key] == $response_indicator->value
                                    || $response_indicator->datatype == 'number' && $order[$response_indicator->key] == $response_indicator->value
                                    || $response_indicator->datatype == 'boolean' && $order[$response_indicator->key] == $response_indicator->value
                                    || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->key])
                                ))
                                || ($response_indicator->index != null && isset($order[$response_indicator->index][$response_indicator->key]) && (
                                    $response_indicator->datatype == 'string' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                    || $response_indicator->datatype == 'number' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                    || $response_indicator->datatype == 'boolean' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                    || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->index][$response_indicator->key])
                                ))
                            ) {
                                $var_referenceid = $response_setting->referenceid ?? null;
                                $var_price = $response_setting->price ?? null;
                                $var_status = $response_setting->status ?? null;
                                $var_note = $response_setting->note ?? null;
                                $var_sn = $response_setting->sn ?? null;
                                $var_errorrefund = $response_setting->errorrefund;

                                $exploding_errorefund = explode('[#]', strtolower($var_errorrefund ?? ''));

                                if ($response_setting->index != null) {
                                    $order = $order[$response_setting->index] ?? null;
                                }

                                $referenceid = $var_referenceid != null ? ($order[$var_referenceid] ?? null) : null;
                                $price = $var_price != null ? ($order[$var_price] ?? null) : null;
                                $status = $var_status != null ? ($order[$var_status] ?? null) : null;
                                $note = $var_note != null ? ($order[$var_note] ?? null) : null;
                                $sn = $var_sn != null ? ($order[$var_sn] ?? null) : null;

                                if ($status != null) {
                                    if (in_array($status, $exploding_errorefund)) {
                                        log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing error refund, response: ' . json_encode($order), $this->merchant->id);

                                        throw new Exception('Gagal melakukan transaksi');
                                    }
                                } else {
                                    if ($var_status == null) {
                                        $status = 'pending';
                                    } else if ($var_status != null && $status == null) {
                                        log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing status, response: ' . json_encode($order), $this->merchant->id);

                                        throw new Exception('Gagal melakukan transaksi');
                                    }
                                }

                                $update = array();
                                $update['jsonresponse'] = json_encode($order);

                                if ($referenceid != null) {
                                    $update['servercode'] = $referenceid;
                                }

                                if ($price != null) {
                                    $update['price'] = $price;
                                }

                                if ($status != null) {
                                    $update['status'] = $status;
                                }

                                if ($note != null) {
                                    $update['note'] = $note;
                                }

                                if ($sn != null) {
                                    $update['sn'] = $sn;
                                }

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponse(array(
                                    'RESULT' => 'OK',
                                    'MESSAGE' => 'Transaksi berhasil',
                                    'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
                                ));
                            } else {
                                log_message_user('error', '[MULTIVENDOR ORDER] Failed to parse response, response: ' . json_decode($order), $this->merchant->id);

                                throw new Exception('Gagal melakukan transaksi');
                            }
                        }
                    }
                }
            }

            $this->db->trans_commit();

            $this->send_notification($insert_id, $this->merchant->id, $currentPhone);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'MESSAGE' => 'Transaksi berhasil',
                'REDIRECT' => base_url('history/detail/' . $insert['clientcode'] . '?userid=' . $this->userid)
            ));
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function pricelist()
    {
        $data = array();
        $data['title'] = $this->merchant->companyname;

        if (getCurrentThemeConfiguration($this->merchant->id) == 'Sobat-Serverppob') {
            $data['content'] = 'landing/pricelist';

            $vendorsmm = getCurrentVendor('SMM', $this->merchant->id);
            $vendorppob = getCurrentVendor('PPOB', $this->merchant->id);

            $where = array(
                'a.userid' => $this->merchant->id,
                "(a.category_apikey = 'PPOB' OR a.category_apikey = 'SMM') =" => true,
                "(a.subcategory_apikey = 'PRABAYAR' OR a.subcategory_apikey = 'SMM') =" => true,
                "(a.vendor = '$vendorppob' OR a.vendor = '$vendorsmm' OR a.vendor IS NULL) =" => true,
                'b.id' => null,
                'a.status' => 1,
                'a.category !=' => null
            );

            $data['category'] = $this->msproduct->select('a.category')
                ->join('disabledcategory b', 'b.categoryname = a.category AND b.category_apikey = a.category_apikey AND b.userid = a.userid', 'LEFT')
                ->where($where)
                ->group_by('a.category')
                ->order_by('a.category')
                ->get()
                ->result();

            return viewTemplate($this->merchant->id, "master", $data);
        }
    }

    public function datatables_pricelist()
    {
        if (getCurrentThemeConfiguration($this->merchant->id) != 'Sobat-Serverppob') {
            return show_404();
        }

        $category = getPost('category', null);
        $brand = getPost('brand', null);
        $searchfilter = getPost('searchfilter', null);

        $data = array();

        $vendorsmm = getCurrentVendor('SMM', $this->merchant->id);
        $vendorppob = getCurrentVendor('PPOB', $this->merchant->id);
        $datatables = $this->datatables->make('MsProduct', 'QueryDatatables', 'SearchDatatables');

        $where = array(
            'a.userid' => $this->merchant->id,
            "(a.category_apikey = 'PPOB' OR a.category_apikey = 'SMM') =" => true,
            "(a.subcategory_apikey = 'PRABAYAR' OR a.subcategory_apikey = 'SMM') =" => true,
            "(a.vendor = '$vendorppob' OR a.vendor = '$vendorsmm' OR a.vendor IS NULL) =" => true,
            'b.id' => null,
            'a.status' => 1,
        );

        if ($category != null) {
            $where['a.category'] = $category;
        } else {
            $where['a.category !='] = null;
        }

        if ($brand != null) {
            $where['a.brand'] = $brand;
        }

        if ($searchfilter != null) {
            $where['a.productname LIKE'] = '%' . $searchfilter . '%';
        }

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $disabledbrand = $this->disabledbrand->result(array(
            'userid' => $this->merchant->id,
        ));

        $disabled_brand = array();
        foreach ($disabledbrand as $key => $value) {
            $disabled_brand[] = $value->brandname;
        }

        if (count($disabled_category) > 0) {
            $where[] = array(
                'a.category !=' => $disabled_category
            );
        }

        if (count($disabled_brand) > 0) {
            $where[] = array(
                'a.brand !=' => $disabled_brand
            );
        }

        $currentuser = getCurrentUser();

        if ($currentuser->roleid ?? null == null) {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'isdefault' => '1'
            ))->row();
        } else {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'id' => $currentuser->roleid
            ))->row();
        }

        if ($getrole != null) {
            if ($getrole->discounttype == 'Simple') {
                $getrolediscount = $this->msrolediscount->get(array(
                    'userid' => $this->merchant->id,
                    'roleid' => $getrole->id
                ))->row();

                $discount = $getrolediscount->trxdiscount ?? 0;
            } else {
                $discount = $this->msrolediscountadv->get(array(
                    'createdby' => $this->merchant->id,
                    'roleid' => $getrole->id,
                    "(servicetype = 'Prabayar' OR servicetype = 'SMM') =" => true
                ))->result();
            }
        } else {
            $getrole = new stdClass();
            $getrole->discounttype = 'Simple';
            $discount = 0;
        }

        foreach ($datatables->getData($where) as $key => $value) {
            $detail = array();
            $detail[] = $key + 1;
            $detail[] = $value->productname;
            $detail[] = $value->category;
            $detail[] = $value->brand ?? '-';
            $detail[] = $value->description ?? '-';
            if ($getrole->discounttype == 'Simple') {
                $detail[] = IDR($value->price - $discount);
            } else {
                $found = false;

                foreach ($discount as $val) {
                    if ($found) continue;

                    if ($val->startrange <= $value->price && $val->endrange >= $value->price && strtoupper($val->servicetype) == 'PRABAYAR') {
                        if ($val->discounttype == 'Persentase') {
                            $detail[] = IDR($value->price - ($value->price * $val->nominal / 100));

                            $found = true;
                        } else {
                            $detail[] = IDR($value->price - $val->nominal);

                            $found = true;
                        }
                    }
                }

                if ($found == false) {
                    $detail[] = IDR($value->price);
                }
            }
            $detail[] = '<span class="badge bg-success">Normal</span>';

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function pricelist_brand()
    {

        if (getCurrentThemeConfiguration($this->merchant->id) == 'Sobat-Serverppob') {
            $category = getPost('category');

            $vendorsmm = getCurrentVendor('SMM', $this->merchant->id);
            $vendorppob = getCurrentVendor('PPOB', $this->merchant->id);

            $where = array(
                'a.userid' => $this->merchant->id,
                "(a.category_apikey = 'PPOB' OR a.category_apikey = 'SMM') =" => true,
                "(a.subcategory_apikey = 'PRABAYAR' OR a.subcategory_apikey = 'SMM') =" => true,
                "(a.vendor = '$vendorppob' OR a.vendor = '$vendorsmm' OR a.vendor IS NULL) =" => true,
                'b.id' => null,
                'a.status' => 1,
                'a.category !=' => null
            );

            if ($category != null) {
                $where['a.category'] = $category;
            }

            $disabledbrand = $this->disabledbrand->result(array(
                'userid' => $this->merchant->id,
            ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            if (count($disabled_brand) > 0) {
                $this->msproduct->where_not_in('a.brand', $disabled_brand);
            }

            $get = $this->msproduct->select('a.brand')
                ->join('disabledcategory b', 'b.categoryname = a.category AND b.category_apikey = a.category_apikey AND b.userid = a.userid', 'LEFT')
                ->where($where)
                ->group_by('a.brand')
                ->order_by('a.brand')
                ->get()
                ->result();

            $option = '<option value="">Pilih Brand</option>';
            foreach ($get as $value) {
                $option .= '<option value="' . $value->brand . '">' . $value->brand . '</option>';
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $option
            ));
        }
    }
    public function average_time()
    {
        
        $merchantId = $this->merchant->id;
        $result = $this->msproduct->getAverageProcessingTime($merchantId);

        if (!$result || $result->total_transactions == 0) {
            return $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'ERROR',
                    'message' => 'Data tidak ditemukan',
                    'data' => []
                ]));
        }

        $total = (int) $result->total_seconds;
        $avg = (int) $result->average_seconds;

        $format = function ($seconds) {
            $h = floor($seconds / 3600);
            $m = floor(($seconds % 3600) / 60);
            $s = $seconds % 60;
            return sprintf('%02d jam %02d menit %02d detik', $h, $m, $s);
        };

        return $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'status' => 'SUCCESS',
                'data' => [
                    'total_transactions' => $result->total_transactions,
                    'total_time_formatted' => $format($total),
                    'average_time_formatted' => $format($avg),
                ]
            ]));
    }
}