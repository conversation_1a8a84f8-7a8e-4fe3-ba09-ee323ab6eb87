<?php

use Duitku\Api;
use Midtrans\Config;
use Midtrans\Snap;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsPaymentMethod $mspaymentmethod
 * @property MsPaymentGateway $mspaymentgateway
 * @property CI_Output $output
 * @property FeePaymentGateway $feepaymentgateway
 * @property MobileSession $mobilesession
 * @property MsUsers $msusers
 * @property Deposits $deposits
 * @property UserBuyNotificationHandler $userbuynotificationhandler
 * @property ApiKeys_Whatsapp $apikeys_whatsapp
 */
class Deposit extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsPaymentMethod', 'mspaymentmethod');
        $this->load->model('MsPaymentGateway', 'mspaymentgateway');
        $this->load->model('FeePaymentGateway', 'feepaymentgateway');
        $this->load->model('MobileSession', 'mobilesession');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('Deposits', 'deposits');
        $this->load->model('UserBuyNotificationHandler', 'userbuynotificationhandler');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
    }

    private function send_notification($depositid, $userid, $phonenumber)
    {
        // Kirim Firebase notification untuk deposit
        sendFirebaseNotificationDeposit($depositid, $userid);

        $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
            'userid' => $userid,
        ));

        if ($apikeys_whatsapp->num_rows() == 0) {
            return false;
        }

        $row = $apikeys_whatsapp->row();

        $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

        $messagenotification = replaceParameterNotificationDeposit($depositid, $userid);
        if ($messagenotification != null && $phonenumber != null) {
            $phonenumber = changePrefixPhone($phonenumber);

            $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

            if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
            }

            return true;
        }

        return false;
    }

    public function paymentmethod_manual()
    {
        $paymentmethod = $this->mspaymentmethod->select('id, paymentmethod, minnominal, maxnominal, feetype')
            ->result(array(
                'userid' => $this->merchant->id,
                "(isdisabled IS NULL OR isdisabled = 0) =" => true
            ));

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Berhasil mengambil data',
            'data' => $paymentmethod
        ));
    }

    public function paymentmethod_otomatis()
    {
        $paymentgateway = $this->mspaymentgateway->result(array(
            'userid' => $this->merchant->id,
            "(isdisabled IS NULL OR isdisabled = 0) =" => true
        ));

        $addons_select = array();
        foreach ($paymentgateway as $key => $value) {
            if ($value->type == 'Payment Gateway') {
                if ($value->vendor == 'Midtrans') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->enabled_payments)) {
                        foreach ($addons->enabled_payments as $k => $v) {
                            $feepayment = $this->feepaymentgateway->get(array(
                                'paymentgatewayid' => $value->id,
                                'paymentname' => $v,
                            ))->row();

                            if (isset(getMidtransPayments()[$v])) {
                                $addons_select[stringEncryption('encrypt', $v)] = array(
                                    'name' => getMidtransPayments()[$v],
                                    'feetype' => $feepayment->feetype ?? null,
                                    'minnominal' => $feepayment->minnominal ?? null,
                                    'maxnominal' => $feepayment->maxnominal ?? null,
                                );
                            }
                        }
                    }
                } elseif ($value->vendor == 'Tripay') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            $feepayment = $this->feepaymentgateway->get(array(
                                'paymentgatewayid' => $value->id,
                                'paymentname' => $v,
                            ))->row();

                            if (isset(getTripayPayments()[$v])) {
                                $addons_select[stringEncryption('encrypt', $v)] = array(
                                    'name' => $addons->alias_payments[$k] ?? getTripayPayments()[$v],
                                    'feetype' => $feepayment->feetype ?? null,
                                    'minnominal' => $feepayment->minnominal ?? null,
                                    'maxnominal' => $feepayment->maxnominal ?? null,
                                );
                            }
                        }
                    }
                } elseif ($value->vendor == 'Duitku') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            $feepayment = $this->feepaymentgateway->get(array(
                                'paymentgatewayid' => $value->id,
                                'paymentname' => $v,
                            ))->row();

                            $addons_select[stringEncryption('encrypt', $v)] = array(
                                'name' => $addons->alias_payments[$k] ?? $addons->channel_payments_name[$k],
                                'feetype' => $feepayment->feetype ?? null,
                                'minnominal' => $feepayment->minnominal ?? null,
                                'maxnominal' => $feepayment->maxnominal ?? null,
                            );
                        }
                    }
                } elseif ($value->vendor == 'PayDisini') {
                    $addons = json_decode(stringEncryption('decrypt', $value->addons));

                    if (isset($addons->channel_payments)) {
                        foreach ($addons->channel_payments as $k => $v) {
                            $feepayment = $this->feepaymentgateway->get(array(
                                'paymentgatewayid' => $value->id,
                                'paymentname' => $v,
                            ))->row();

                            $addons_select[stringEncryption('encrypt', json_encode(array(
                                'id' => $v,
                                'type' => $addons->channel_payments_type[$k]
                            )))] = array(
                                'name' => $addons->alias_payments[$k] ?? $addons->channel_payments_name[$k],
                                'feetype' => $feepayment->feetype ?? null,
                                'minnominal' => $feepayment->minnominal ?? null,
                                'maxnominal' => $feepayment->maxnominal ?? null,
                            );
                        }
                    }
                }
            } else {
                $addons_select[stringEncryption('encrypt', $value->id)] = array(
                    'name' => $value->type,
                    'feetype' => $value->feetype,
                    'minnominal' => $value->minnominal,
                    'maxnominal' => $value->maxnominal,
                );
            }
        }

        $notificationhandler = $this->userbuynotificationhandler->select('a.*, b.packagename')
            ->join('msnotificationhandlerpackage b', 'b.id = a.notificationid')
            ->result(array(
                'a.userid' => $this->merchant->id,
                'b.isactive' => 1,
                "(isdisabled IS NULL OR isdisabled = 0) =" => true
            ));


        foreach ($notificationhandler as $key => $value) {
            $keys = stringEncryption('encrypt', json_encode(array(
                'id' => $value->id,
                'type' => 'Notification Handler'
            )));

            $addons_select[$keys] = array(
                'name' => $value->packagename,
                'feetype' => $value->feetype,
                'minnominal' => $value->minnominal,
                'maxnominal' => $value->maxnominal,
            );
        }

        asort($addons_select);

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Berhasil mengambil data',
            'data' => $addons_select
        ));
    }

    private function validateToken($token)
    {
        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = stringEncryption('decrypt', $token);

        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = json_decode($token);

        if (json_last_error() != JSON_ERROR_NONE) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $validate = $this->mobilesession->total(array(
            'token' => getPost('token')
        ));

        if ($validate == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $user = $this->msusers->get(array(
            'id' => $token->id,
            'merchantid' => $this->merchant->id,
        ));

        if ($user->num_rows() == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $row = $user->row();

        return array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $row
        );
    }

    public function request()
    {
        try {
            $this->db->trans_begin();

            $token = getPost('token');
            $paymentmethod = getPost('paymentmethod');
            $payment = getPost('payment');
            $nominal = getPost('nominal');
            $pin = getPost('pin');
            $phone = getPost('phone');

            if ($paymentmethod == null) {
                throw new Exception('Metode pembayaran tidak boleh kosong');
            }

            if ($payment == null) {
                throw new Exception('Metode pembayaran tidak boleh kosong');
            }

            if ($nominal == null) {
                throw new Exception('Nominal tidak boleh kosong');
            }

            if ($nominal <= 0) {
                throw new Exception('Nominal tidak boleh kurang dari atau sama dengan 0');
            }

            if ($pin == null) {
                throw new Exception('PIN tidak boleh kosong');
            }

            $validate = $this->validateToken($token);

            if ($validate['status'] == false) {
                throw new Exception($validate['message']);
            }

            if (stringEncryption('encrypt', $pin) != $validate['data']->pin) {
                throw new Exception('PIN yang anda masukkan salah');
            }

            $currentuser = $validate['data'];
            $userid = $currentuser->id;

            if ($currentuser->is_kyc != 1 && $nominal > 500000) {
                throw new Exception('Maksimal deposit untuk akun yang belum KYC adalah Rp 500.000');
            }

            $spamCheck = $this->deposits->total(array(
                'userid' => $userid,
                'status' => 'Pending'
            ));

            if ($spamCheck >= 2) {
                throw new Exception('Terdapat 2 permintaan topup yang masih pending, Silahkan selesaikan pembayaran terlebih dahulu');
            }

            $limit = $this->msusers->get(array(
                'id' => $this->merchant->id,
            ))->row();

            if ($limit->balancelimit != null && $limit->balancelimit > 0) {
                if ($nominal + $currentuser->balance > $limit->balancelimit) {
                    throw new Exception('Anda melebihi batas Maksimal Penyimpanan Saldo, Jumlah saldo Maksimal yang dapat disimpan ' . IDR($limit->balancelimit));
                }
            }

            $insert = array();
            $insert['userid'] = $userid;
            $insert['code'] = generateTransactionNumber('DEPOSIT');
            $insert['status'] = 'Pending';
            $insert['merchantid'] = $this->merchant->id;
            $insert['phonenumber'] = $currentuser->phonenumber;
            $insert['depositplatform'] = 'apps';

            if ($paymentmethod == 'Manual') {
                $get = $this->mspaymentmethod->get(array(
                    'userid' => $this->merchant->id,
                    'id' => $payment
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Metode pembayaran tidak ditemukan');
                }

                $row = $get->row();

                if ($row->isdisabled == 1) {
                    throw new Exception('Metode pembayaran tidak tersedia');
                } elseif ($row->minnominal > $nominal) {
                    throw new Exception('Minimal topup ' . IDR($row->minnominal));
                } elseif ($row->maxnominal != 0 && $row->maxnominal < $nominal) {
                    throw new Exception('Maksimal topup ' . IDR($row->maxnominal));
                } elseif ($row->isunique == 1) {
                    if ($this->merchant->uniquenominal_start != null && $this->merchant->uniquenominal_end != null && $this->merchant->uniquenominal_start != 0 && $this->merchant->uniquenominal_end != 0) {
                        $unique = rand($this->merchant->uniquenominal_start, $this->merchant->uniquenominal_end);
                    } else {
                        $unique = rand(100, 999);
                    }

                    $nominal = $nominal + $unique;

                    if ($row->uniqueadmin == 1) {
                        $insert['uniqueadmin'] = $unique;
                    }
                }

                $insert['payment'] = $row->paymentmethod;
                $insert['nominal'] = $nominal;

                if ($row->image == null) {
                    $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($insert['nominal']) . ",- Ke Rekening: $row->paymentmethod, no. $row->accountnumber a.n. $row->accountname. Batas waktu transfer 24 jam";
                } else {
                    $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($insert['nominal']) . ",- Ke Rekening: $row->paymentmethod, a.n. $row->accountname. Batas waktu transfer 24 jam";
                }
                $insert['paymenttype'] = 'Manual';
                $insert['paymentmethodid'] = $row->id;

                if ($row->isbonus == 1) {
                    $insert['isbonus'] = 1;

                    if ($row->bonustype == 'Nominal') {
                        $insert['nominalbonus'] = $row->nominalbonus;
                    } else if ($row->bonustype == 'Persentase') {
                        $insert['nominalbonus'] = $nominal * (((int) $row->nominalbonus) / 100);
                        $insert['percentagebonus'] = $row->nominalbonus;
                    }
                }

                if ($row->feetype != null) {
                    $feeamount = 0;

                    if ($row->feetype == 'Persentase') {
                        $feeamount = $nominal * (((int) $row->nominalfee) / 100);
                    } else if ($row->feetype == 'Nominal') {
                        $feeamount = $row->nominalfee;
                    }

                    if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                        throw new Exception('Nominal Topup tidak boleh kurang dari biaya admin');
                    }

                    $insert['fee'] = $feeamount;
                }
            } else if ($paymentmethod == 'Otomatis') {
                $payment = stringEncryption('decrypt', $payment);
                $insert['paymenttype'] = 'Otomatis';

                if (!is_numeric($payment)) {
                    $json = json_decode($payment);

                    if (!is_object($json) && !isset($json->type)) {
                        $paymentgateway = $this->mspaymentgateway->get(array(
                            'userid' => $this->merchant->id,
                            'type' => 'Payment Gateway',
                            "(isdisabled IS NULL OR isdisabled = 0) =" => true
                        ))->row();

                        if ($paymentgateway == null) {
                            throw new Exception('Metode pembayaran tidak ditemukan');
                        }

                        if ($paymentgateway->isdisabled == 1) {
                            throw new Exception('Metode pembayaran tidak tersedia');
                        } else if ($paymentgateway->minnominal > $nominal) {
                            throw new Exception('Minimal topup ' . IDR($paymentgateway->minnominal));
                        } elseif ($paymentgateway->maxnominal != 0 && $paymentgateway->maxnominal < $nominal) {
                            throw new Exception('Maksimal topup ' . IDR($paymentgateway->maxnominal));
                        }

                        if ($paymentgateway != null && $paymentgateway->vendor == 'Midtrans' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->enabled_payments)) {
                                if (in_array($payment, $addons->enabled_payments) && isset(getMidtransPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;

                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $nominal * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                                            throw new Exception('Nominal Topup tidak boleh kurang dari biaya admin');
                                        }

                                        $insert['fee'] = $feeamount;
                                    }

                                    Config::$serverKey = $detail->serverkey;
                                    Config::$clientKey = $detail->clientkey;
                                    Config::$isProduction = ENVIRONMENT == 'production' ? true : false;

                                    $params = array(
                                        'transaction_details' => array(
                                            'order_id' => $insert['code'],
                                            'gross_amount' => $nominal,
                                        ),
                                        'enabled_payments' => array(
                                            $payment
                                        )
                                    );

                                    try {
                                        $snapToken = Snap::getSnapToken($params);
                                    } catch (Exception $snapex) {
                                        if ($snapex->getMessage() != null) {
                                            log_message_user('error', "[MIDTRANS TOPUP] Response: " . $snapex->getMessage(), $this->merchant->id);
                                        }

                                        throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                    }

                                    $insert['payment'] = getMidtransPayments()[$payment];
                                    $insert['nominal'] = $nominal;
                                    $insert['gatewayvendor'] = $paymentgateway->vendor;
                                    $insert['snaptoken'] = $snapToken;
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Tripay' && $paymentgateway->addons != null) {
                            if ($payment == 'OVO') {
                                if ($phone == null) {
                                    throw new Exception('Nomor Handphone OVO tidak boleh kosong');
                                } else if (!is_numeric($phone)) {
                                    throw new Exception('Nomor Handphone OVO tidak valid');
                                } else {
                                    $insert['phonenumber'] = $phone;
                                }
                            }

                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getTripayPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;

                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $nominal * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                                            throw new Exception('Nominal Topup tidak boleh kurang dari biaya admin');
                                        }

                                        $insert['fee'] = $feeamount;
                                    }

                                    $tripay = new Tripay($detail->merchantcode, $detail->apikey, $detail->privatekey);
                                    $request = $tripay->requestTransaction($payment, $insert['code'], $nominal, getCurrentUser($userid)->name, getCurrentUser($userid)->email, $phone != null ? $phone : getCurrentUser($userid)->phonenumber, "Deposit Saldo Akun", 1, base_url('deposit/history?userid=' . $this->userid));

                                    if (isset($request->success) && $request->success) {
                                        $insert['payment'] = getTripayPayments()[$payment];
                                        $insert['nominal'] = $nominal;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;
                                        $insert['checkouturl'] = $request->data->checkout_url;
                                        $insert['servercode'] = $request->data->reference;
                                        $insert['jsonresponse'] = json_encode($request);
                                    } else {
                                        if ($request != null) {
                                            log_message_user('error', '[TRIPAY TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                        }

                                        throw new Exception(isset($request->message) ? $request->message : 'Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Duitku' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments) && isset($addons->channel_payments_name) && isset($detail->apikey) && isset($detail->merchantcode)) {
                                if (in_array($payment, $addons->channel_payments)) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;

                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $nominal * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                                            throw new Exception('Nominal Topup tidak boleh kurang dari biaya admin');
                                        }

                                        $insert['fee'] = $feeamount;
                                    }

                                    $params = array(
                                        'paymentAmount' => $nominal,
                                        'paymentMethod' => $payment,
                                        'merchantOrderId' => $insert['code'],
                                        'productDetails' => 'Deposit Saldo Akun',
                                        'customerVaName' => $this->merchant->companyname,
                                        'email' => $currentuser->email,
                                        'callbackUrl' => base_url('callback/duitku'),
                                        'returnUrl' => base_url('deposit/history?userid=' . $this->userid),
                                    );

                                    $apikey = $detail->apikey;
                                    $merchantcode = $detail->merchantcode;

                                    $duitku_config = new \Duitku\Config($apikey, $merchantcode, ENVIRONMENT == 'development', true, false);

                                    $paymentMethod = Api::getPaymentMethod($nominal, $duitku_config);
                                    $paymentMethod = json_decode($paymentMethod);

                                    $paymentName = null;
                                    foreach ($paymentMethod->paymentFee as $key => $value) {
                                        if ($value->paymentMethod == $payment) {
                                            $paymentName = $value->paymentName;
                                        }
                                    }

                                    if ($paymentName == null) {
                                        throw new Exception('Pembayaran tidak ditemukan');
                                    }

                                    $create = Api::createInvoice($params, $duitku_config);
                                    $create = json_decode($create);

                                    if (isset($create->statusCode) && $create->statusCode == '00') {
                                        $insert['payment'] = $paymentName;
                                        $insert['nominal'] = $nominal;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;
                                        $insert['checkouturl'] = $create->paymentUrl;
                                        $insert['servercode'] = $create->reference;
                                        $insert['jsonresponse'] = json_encode($create);
                                    } else {
                                        if ($create != null) {
                                            log_message_user('error', '[DUITKU TOPUP] Response: ' . json_encode($create), $this->merchant->id);
                                        }

                                        throw new Exception($create->statusMessage ?? 'Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else {
                            throw new Exception('Pembayaran tidak ditemukan');
                        }

                        if ($paymentgateway->isbonus == 1) {
                            $insert['isbonus'] = 1;

                            if ($paymentgateway->bonustype == 'Nominal') {
                                $insert['nominalbonus'] = $paymentgateway->nominalbonus;
                            } else {
                                $insert['nominalbonus'] = $nominal * (((int)$paymentgateway->nominalbonus) / 100);
                                $insert['percentagebonus'] = $paymentgateway->nominalbonus;
                            }
                        }
                    } else {
                        if (isset($json->type)) {
                            if ($json->type == 'Notification Handler') {
                                $userbuynotificationhandler = $this->userbuynotificationhandler->select('a.*, b.packagename, b.isqr')
                                    ->join('msnotificationhandlerpackage b', 'b.id = a.notificationid')
                                    ->get(array(
                                        'a.userid' => $this->merchant->id,
                                        'a.id' => $json->id,
                                        'b.isactive' => 1,
                                        "(a.isdisabled IS NULL OR a.isdisabled = 0) =" => true
                                    ));

                                if ($userbuynotificationhandler->num_rows() == 0) {
                                    throw new Exception('Metode pembayaran tidak ditemukan');
                                }

                                $rowuserbuynotificationhandler = $userbuynotificationhandler->row();

                                if ($rowuserbuynotificationhandler->isdisabled == 1) {
                                    throw new Exception('Metode pembayaran tidak tersedia');
                                } else if ($rowuserbuynotificationhandler->minnominal > $nominal) {
                                    throw new Exception('Minimal topup ' . IDR($rowuserbuynotificationhandler->minnominal));
                                } else if ($rowuserbuynotificationhandler->maxnominal < $nominal) {
                                    throw new Exception('Maksimal topup ' . IDR($rowuserbuynotificationhandler->maxnominal));
                                }

                                if ($this->merchant->uniquenominal_start != null && $this->merchant->uniquenominal_end != null && $this->merchant->uniquenominal_start != 0 && $this->merchant->uniquenominal_end != 0) {
                                    $unique = rand($this->merchant->uniquenominal_start, $this->merchant->uniquenominal_end);
                                } else {
                                    $unique = rand(100, 999);
                                }

                                $nominal = $nominal + $unique;

                                $insert['payment'] = $rowuserbuynotificationhandler->packagename;
                                $insert['nominal'] = $nominal;

                                if ($rowuserbuynotificationhandler->isqr != 1) {
                                    $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($nominal) . ",- Ke Rekening: $rowuserbuynotificationhandler->packagename, no. $rowuserbuynotificationhandler->accountnumber a.n. $rowuserbuynotificationhandler->accountname. Batas waktu transfer 24 jam";
                                } else {
                                    $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($nominal) . ",- Ke Rekening: $rowuserbuynotificationhandler->packagename, a.n. $rowuserbuynotificationhandler->accountname. Batas waktu transfer 24 jam";
                                }

                                $insert['paymenttype'] = 'Otomatis';
                                $insert['gatewayvendor'] = 'Notification Handler Services';
                                $insert['paymentmethodid'] = $rowuserbuynotificationhandler->id;

                                if ($rowuserbuynotificationhandler->isbonus == 1) {
                                    $insert['isbonus'] = 1;

                                    if ($rowuserbuynotificationhandler->bonustype == 'Nominal') {
                                        $insert['nominalbonus'] = $rowuserbuynotificationhandler->nominalbonus;
                                    } else {
                                        $insert['nominalbonus'] = $nominal * (((int)$rowuserbuynotificationhandler->nominalbonus) / 100);
                                        $insert['percentagebonus'] = $rowuserbuynotificationhandler->nominalbonus;
                                    }
                                }

                                if ($rowuserbuynotificationhandler->isfee == 1) {
                                    $feeamount = 0;

                                    if ($rowuserbuynotificationhandler->feetype == 'Persentase') {
                                        $feeamount = $nominal * $rowuserbuynotificationhandler->nominalfee / 100;
                                    } else if ($rowuserbuynotificationhandler->feetype == 'Nominal') {
                                        $feeamount = $rowuserbuynotificationhandler->nominalfee;
                                    }

                                    if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                                        throw new Exception('Nominal Topup tidak boleh kurang dari biaya admin');
                                    }

                                    $insert['fee'] = $feeamount;
                                }
                            } else {
                                $paymentgateway = $this->mspaymentgateway->get(array(
                                    'userid' => $this->merchant->id,
                                    'type' => 'Payment Gateway',
                                    "(isdisabled IS NULL OR isdisabled = 0) =" => true
                                ))->row();

                                if ($paymentgateway == null) {
                                    throw new Exception('Metode pembayaran tidak ditemukan');
                                } else if ($paymentgateway->isdisabled == 1) {
                                    throw new Exception('Metode pembayaran tidak tersedia');
                                } else if ($paymentgateway->minnominal > $nominal) {
                                    throw new Exception('Minimal topup ' . IDR($paymentgateway->minnominal));
                                } else if ($paymentgateway->maxnominal != 0 && $paymentgateway->maxnominal < $nominal) {
                                    throw new Exception('Maksimal topup ' . IDR($paymentgateway->maxnominal));
                                }

                                if ($paymentgateway->vendor == 'PayDisini' && $paymentgateway->addons != null && isset($json->id)) {
                                    $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                                    $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                                    $paydisini = new PayDisini($detail->apikey);
                                    $paymentchannel = $paydisini->paymentChannel();

                                    if (isset($paymentchannel->success) && $paymentchannel->success) {
                                        $paymentchannel = $paymentchannel->data;

                                        if (isset($addons->channel_payments)) {
                                            if (in_array($json->id, $addons->channel_payments) && in_array($json->id, array_column($paymentchannel, 'id'))) {
                                                $fee = $this->feepaymentgateway->row(array(
                                                    'paymentgatewayid' => $paymentgateway->id,
                                                    'paymentname' => $json->id
                                                ));

                                                if ($fee != null) {
                                                    $feeamount = 0;

                                                    if ($fee->feetype == 'Persentase') {
                                                        $feeamount = $nominal * $fee->fee / 100;
                                                    } else if ($fee->feetype == 'Nominal') {
                                                        $feeamount = $fee->fee;
                                                    }

                                                    if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                                                        throw new Exception('Nominal Topup tidak boleh kurang dari biaya admin');
                                                    }

                                                    $insert['fee'] = $feeamount;
                                                }

                                                $insert['paydisinicode'] = generateUniqueCodePayDisini();
                                                $request = $paydisini->requestTransaction($json->id, $insert['paydisinicode'], $nominal, 'Deposit Saldo Akun', $phone != null ? $phone : getCurrentUser($userid)->phonenumber, $json->type);

                                                if (isset($request->success) && $request->success) {
                                                    $insert['payment'] = $addons->alias_payments[array_keys($addons->channel_payments, $json->id)[0] ?? ""] ?? $addons->channel_payments_name[array_search($json->id, $addons->channel_payments)];
                                                    $insert['nominal'] = $nominal;
                                                    $insert['gatewayvendor'] = $paymentgateway->vendor;

                                                    if ($json->type == 'VA') {
                                                        $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($nominal) . ",- Ke Rekening: " . ($addons->alias_payments[array_keys($addons->channel_payments, $json->id)[0] ?? ""] ?? $addons->channel_payments_name[array_search($json->id, $addons->channel_payments)]) . ", no. " . $request->data->virtual_account . " Batas waktu transfer 3 jam hingga pukul" . DateFormat($request->data->expired, 'd F Y H:i:s');
                                                    } else if ($json->type == 'Retail') {
                                                        $insert['note'] = "Bayar di " . ($addons->alias_payments[array_keys($addons->channel_payments, $json->id)[0] ?? ""] ?? $addons->channel_payments_name[array_search($json->id, $addons->channel_payments)]) . " sebesar Rp " . IDR($nominal) . ",- dan beritahu kasir kode pembayaran: " . $request->data->payment_code . " Batas waktu transfer 3 jam hingga pukul" . DateFormat($request->data->expired, 'd F Y H:i:s');
                                                    } else {
                                                        $insert['note'] = $request->data->note;
                                                    }

                                                    if ($json->type == 'QRIS') {
                                                        $insert['isqr'] = 1;
                                                    }

                                                    $insert['checkouturl'] = $request->data->checkout_url;
                                                    $insert['servercode'] = $request->data->pay_id;
                                                    $insert['jsonresponse'] = json_encode($request);
                                                } else {
                                                    if ($request != null) {
                                                        log_message_user('error', '[PAYDISINI TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                                    }

                                                    if (isset($request->msg) && $request->msg) {
                                                        throw new Exception($request->msg);
                                                    } else {
                                                        throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                                    }
                                                }
                                            } else {
                                                throw new Exception('Pembayaran tidak ditemukan');
                                            }
                                        } else {
                                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                        }
                                    } else {
                                        throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Metode pembayaran tidak ditemukan');
                                }
                            }
                        } else {
                            throw new Exception('Metode pembayaran tidak ditemukan');
                        }
                    }
                } else {
                    $paymentgateway = $this->mspaymentgateway->get(array(
                        'id' => $payment,
                        'userid' => $this->merchant->id,
                        "(isdisabled IS NULL OR isdisabled = 0) =" => true
                    ));

                    if ($paymentgateway->num_rows() == 0) {
                        throw new Exception('Pembayaran tidak ditemukan');
                    }

                    $rowpaymentgateway = $paymentgateway->row();

                    if ($rowpaymentgateway->isdisabled == 1) {
                        throw new Exception('Metode pembayaran tidak tersedia');
                    } else if ($rowpaymentgateway->minnominal > $nominal) {
                        throw new Exception('Minimal topup ' . IDR($rowpaymentgateway->minnominal));
                    } else if ($rowpaymentgateway->maxnominal != 0 && $rowpaymentgateway->maxnominal < $nominal) {
                        throw new Exception('Maksimal topup ' . IDR($rowpaymentgateway->maxnominal));
                    }

                    $detail = json_decode(stringEncryption('decrypt', $rowpaymentgateway->detail));

                    $insert['payment'] = $rowpaymentgateway->type;

                    if ($this->merchant->uniquenominal_start != null && $this->merchant->uniquenominal_end != null && $this->merchant->uniquenominal_start != 0 && $this->merchant->uniquenominal_end != 0) {
                        $unique = rand($this->merchant->uniquenominal_start, $this->merchant->uniquenominal_end);

                        $insert['nominal'] = $nominal + $unique;
                    } else {
                        $insert['nominal'] = $nominal + rand(100, 999);
                    }

                    if ($rowpaymentgateway->vendor == 'BCA') {
                        $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($insert['nominal']) . ",- Ke Rekening: BCA, no. $detail->accountnumber a.n. $detail->accountname. Batas waktu transfer 24 jam";
                    } else if ($rowpaymentgateway->vendor == 'GOPAY') {
                        $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($insert['nominal']) . ",- Ke Rekening: GOPAY, no. $detail->phonenumber. Batas waktu transfer 24 jam";
                    }

                    if ($rowpaymentgateway->isbonus == 1) {
                        $insert['isbonus'] = 1;

                        if ($rowpaymentgateway->bonustype == 'Nominal') {
                            $insert['nominalbonus'] = $rowpaymentgateway->nominalbonus;
                        } else {
                            $insert['nominalbonus'] = $nominal * (((int)$rowpaymentgateway->nominalbonus) / 100);
                            $insert['percentagebonus'] = $rowpaymentgateway->nominalbonus;
                        }
                    }
                }
            } else {
                throw new Exception('Metode pembayaran tidak ditemukan');
            }

            $this->deposits->insert($insert);
            $depositid = $this->db->insert_id();

            $this->send_notification($depositid, $this->merchant->id, $currentuser->phonenumber);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Permintaan topup gagal');
            }

            $this->db->trans_commit();

            $this->output->set_status_header(200);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => true,
                'message' => 'Permintaan topup berhasil',
                'depositid' => $depositid,
            ));
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => $ex->getMessage(),
            ));
        }
    }

    public function detail()
    {
        $token = getPost('token');
        $depositid = getPost('depositid');

        if ($depositid == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Deposit tidak ditemukan',
            ));
        }

        $validate = $this->validateToken($token);

        if ($validate['status'] == false) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => $validate['message'],
            ));
        }

        $deposit = $this->deposits->select('a.*, b.accountname, b.accountnumber, b.image, c.qrimage')
            ->join('mspaymentmethod b', 'b.id = a.paymentmethodid', 'left')
            ->join('userbuynotificationhandler c', "c.id = a.paymentmethodid and a.gatewayvendor = 'Notification Handler Services'", 'left')
            ->get(array(
                'a.id' => $depositid,
                'a.userid' => $validate['data']->id,
            ));

        if ($deposit->num_rows() == 0) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Deposit tidak ditemukan',
            ));
        }

        $rowdeposit = $deposit->row();

        if ($rowdeposit->paymenttype == 'Otomatis' && $rowdeposit->gatewayvendor == 'Midtrans') {
            $paymentgateway = $this->mspaymentgateway->get(array(
                'userid' => $this->merchant->id,
                'type' => 'Payment Gateway',
                'vendor' => 'Midtrans',
                "(isdisabled IS NULL OR isdisabled = 0) =" => true
            ));

            if ($paymentgateway->num_rows() > 0) {
                $row_paymentgateway = $paymentgateway->row();

                $detail = json_decode(stringEncryption('decrypt', $row_paymentgateway->detail));
                $rowdeposit->midtrans_clientkey = $detail->clientkey;
            } else {
                $rowdeposit->midtrans_clientkey = null;
            }
        } else {
            $rowdeposit->midtrans_clientkey = null;
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $rowdeposit
        ));
    }
}
