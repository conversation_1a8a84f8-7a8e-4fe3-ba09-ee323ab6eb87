<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-28 09:34:30 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 09:34:39 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 09:34:40 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:02:57 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:05:22 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:05:23 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:23:11 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:23:12 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:23:12 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:23:13 --> Query error: Unknown column 'a.updatedate' in 'field list' - Invalid query: SELECT COUNT(*) AS `numrows`
FROM `msproduct` `a`
LEFT JOIN `disabledcategory` `b` ON `b`.`categoryname` = `a`.`category` AND `b`.`category_apikey` = `a`.`category_apikey` AND `b`.`userid` = `a`.`userid`
LEFT JOIN `productfavourite` `c` ON `c`.`productid` = `a`.`id` AND `c`.`userid` = 0
LEFT JOIN (SELECT a.serviceid, SEC_TO_TIME(AVG(TIMESTAMPDIFF(SECOND, a.createddate, a.updatedate))) AS rata2 FROM trorder a JOIN msproduct b ON b.id = a.serviceid WHERE LOWER(a.status) IN ('success', 'sukses', 'completed') AND a.merchanthid_order = b.merchant_id GROUP BY a.serviceid) d ON `d`.`serviceid` = `a`.`id`
WHERE `a`.`userid` = '28'
AND `a`.`category_apikey` = 'PPOB'
AND `b`.`id` IS NULL
AND `a`.`status` = 1
AND (`a`.`vendor` = 'Digiflazz' OR `a`.`vendor` IS NULL ) = 1
AND `a`.`vendorid` IS NULL
ERROR - 2025-06-28 10:23:13 --> Severity: error --> Exception: Call to a member function num_rows() on bool C:\xampp\htdocs\serverppob\live\demo\system\database\DB_query_builder.php 1349
ERROR - 2025-06-28 10:24:03 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:04 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:04 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:04 --> Query error: Unknown column 'a.updatedate' in 'field list' - Invalid query: SELECT COUNT(*) AS `numrows`
FROM `msproduct` `a`
LEFT JOIN `disabledcategory` `b` ON `b`.`categoryname` = `a`.`category` AND `b`.`category_apikey` = `a`.`category_apikey` AND `b`.`userid` = `a`.`userid`
LEFT JOIN `productfavourite` `c` ON `c`.`productid` = `a`.`id` AND `c`.`userid` = 0
LEFT JOIN (SELECT a.serviceid, SEC_TO_TIME(AVG(TIMESTAMPDIFF(SECOND, a.createddate, a.updatedate))) AS rata2 FROM trorder a JOIN msproduct b ON b.id = a.serviceid WHERE LOWER(a.status) IN ('success', 'sukses', 'completed') AND a.merchanthid_order = b.merchant_id GROUP BY a.serviceid) d ON `d`.`serviceid` = `a`.`id`
WHERE `a`.`userid` = '28'
AND `a`.`category_apikey` = 'PPOB'
AND `b`.`id` IS NULL
AND `a`.`status` = 1
AND (`a`.`vendor` = 'Digiflazz' OR `a`.`vendor` IS NULL ) = 1
AND `a`.`vendorid` IS NULL
ERROR - 2025-06-28 10:24:04 --> Severity: error --> Exception: Call to a member function num_rows() on bool C:\xampp\htdocs\serverppob\live\demo\system\database\DB_query_builder.php 1349
ERROR - 2025-06-28 10:24:04 --> Query error: Unknown column 'a.updatedate' in 'field list' - Invalid query: SELECT COUNT(a.id) as total_transactions, SUM(TIMESTAMPDIFF(SECOND, `a`.`createddate`, a.updatedate)) as total_time_seconds, AVG(TIMESTAMPDIFF(SECOND, `a`.`createddate`, a.updatedate)) as average_time_seconds
FROM `trorder` `a`
INNER JOIN `msproduct` `b` ON `b`.`id` = `a`.`serviceid`
WHERE `a`.`merchanthid_order` = '28'
AND LOWER(a.status) IN('success', 'sukses', 'completed')
AND `a`.`updatedate` IS NOT NULL
AND `a`.`createddate` IS NOT NULL
ERROR - 2025-06-28 10:24:04 --> Severity: error --> Exception: Call to a member function row() on bool C:\xampp\htdocs\serverppob\live\demo\application\controllers\Landing.php 375
ERROR - 2025-06-28 10:24:11 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:11 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:11 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:11 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:24 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:25 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:25 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:26 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:26 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:26 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:24:26 --> Query error: Unknown column 'a.updatedate' in 'field list' - Invalid query: SELECT COUNT(*) AS `numrows`
FROM `msproduct` `a`
LEFT JOIN `disabledcategory` `b` ON `b`.`categoryname` = `a`.`category` AND `b`.`category_apikey` = `a`.`category_apikey` AND `b`.`userid` = `a`.`userid`
LEFT JOIN `productfavourite` `c` ON `c`.`productid` = `a`.`id` AND `c`.`userid` = 0
LEFT JOIN (SELECT a.serviceid, SEC_TO_TIME(AVG(TIMESTAMPDIFF(SECOND, a.createddate, a.updatedate))) AS rata2 FROM trorder a JOIN msproduct b ON b.id = a.serviceid WHERE LOWER(a.status) IN ('success', 'sukses', 'completed') AND a.merchanthid_order = b.merchant_id GROUP BY a.serviceid) d ON `d`.`serviceid` = `a`.`id`
WHERE `a`.`userid` = '28'
AND `a`.`category_apikey` = 'PPOB'
AND `b`.`id` IS NULL
AND `a`.`status` = 1
AND (`a`.`vendor` = 'Digiflazz' OR `a`.`vendor` IS NULL ) = 1
AND `a`.`vendorid` IS NULL
ERROR - 2025-06-28 10:24:26 --> Severity: error --> Exception: Call to a member function num_rows() on bool C:\xampp\htdocs\serverppob\live\demo\system\database\DB_query_builder.php 1349
ERROR - 2025-06-28 10:24:27 --> Query error: Unknown column 'a.updatedate' in 'field list' - Invalid query: SELECT COUNT(a.id) as total_transactions, SUM(TIMESTAMPDIFF(SECOND, `a`.`createddate`, a.updatedate)) as total_time_seconds, AVG(TIMESTAMPDIFF(SECOND, `a`.`createddate`, a.updatedate)) as average_time_seconds
FROM `trorder` `a`
INNER JOIN `msproduct` `b` ON `b`.`id` = `a`.`serviceid`
WHERE `a`.`merchanthid_order` = '28'
AND LOWER(a.status) IN('success', 'sukses', 'completed')
AND `a`.`updatedate` IS NOT NULL
AND `a`.`createddate` IS NOT NULL
ERROR - 2025-06-28 10:24:27 --> Severity: error --> Exception: Call to a member function row() on bool C:\xampp\htdocs\serverppob\live\demo\application\controllers\Landing.php 375
ERROR - 2025-06-28 10:44:12 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:44:13 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:44:13 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:44:13 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:44:13 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:44:15 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:44:39 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:44:40 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:44:53 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:44:54 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:04 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:04 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:04 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:04 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:07 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:08 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:08 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:09 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:09 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:09 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:29 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:29 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:29 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:30 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:30 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:30 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:31 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:31 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:31 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:32 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:32 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:32 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:32 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:45:33 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:46:10 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:46:11 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:46:11 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:46:11 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:46:12 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:46:12 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:52:18 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:52:20 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:52:20 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:52:20 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:52:20 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 10:52:20 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 11:01:40 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 11:01:41 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 11:01:41 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 11:01:41 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 11:01:41 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 11:01:41 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 11:04:55 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 11:04:56 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 11:04:56 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 11:04:57 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 11:04:57 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
ERROR - 2025-06-28 11:04:57 --> Could not find the specified $config['composer_autoload'] path: ./vendor/autoload.php
