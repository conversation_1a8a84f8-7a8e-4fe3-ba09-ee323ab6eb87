<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsProduct extends MY_Model
{
    protected $table = 'msproduct';
    public $SearchDatatables = array(
        'a.code',
        'a.productname'
    );

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.id AS disabledid, c.id AS favouriteid')
            ->from($this->table . ' a')
            ->join('disabledcategory b', 'b.categoryname = a.category AND b.category_apikey = a.category_apikey AND b.userid = a.userid', 'LEFT')
            ->join('productfavourite c', 'c.productid = a.id AND c.userid = ' . (getCurrentIdUser() ?? '0'), 'LEFT')
            ->order_by('a.price, a.category, a.brand');

        return $this;
    }
    public function getAverageProcessingTime($merchantId)
    {
        $this->db->select('
        COUNT(*) AS total_transactions,
        SUM(TIMESTAMPDIFF(SECOND, createddate, updatedate)) AS total_seconds,
        AVG(TIMESTAMPDIFF(SECOND, createddate, updatedate)) AS average_seconds
    ');
        $this->db->from('trorder');
        $this->db->join('msproduct', 'trorder.merchanthid_order = msproduct.merchant_id');
        $this->db->where_in('trorder.status', ['success', 'sukses', 'completen']);
        $this->db->where('trorder.merchantid_order', $merchantId);

        return $this->db->get()->row();
    }
}