<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<style>
.pagination li a {
    width: unset;
    height: unset;
    line-height: unset;
    border-radius: unset;
}

div.dataTables_processing>div:last-child div {
    background-color: #ccc;
}
</style>

<header class="pages valign bg-img parallaxie" data-background="<?= base_url('assets/fula') ?>/img/bg.jpg"
    data-overlay-dark="7">
    <div class="container">
        <div class="row">
            <div class="full-width mt-80">
                <h5>Produk Digital</h5>
                <h6>
                    <a href="<?= base_url() ?>">Home</a>/<a href="javascript:;" class="active">Produk Digital</a>
                </h6>
            </div>
        </div>
    </div>
</header>

<section class="section-padding bg-gray half">
    <div class="container">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-end mb-4">
                    <div class="col-md-3">
                        <label for="">Kategori</label>
                        <select name="category" class="form-control">
                            <option value="">- Pilih -</option>

                            <?php foreach ($category as $key => $value) : ?>
                            <option value="<?= $value->category ?>"><?= $value->category ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="">Brand</label>
                        <select name="brand" class="form-control">
                            <option value="">- Pilih -</option>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <button type="button" class="btn btn-primary" onclick="filter()">Filter</button>
                    </div>
                </div>

                <table class="table table-bordered table-striped table-hover" id="datatables">
                    <thead>
                        <tr>
                            <th>Nama Produk</th>
                            <th>Kategori Produk</th>
                            <th>Brand</th>
                            <th>Deskripsi</th>
                            <th>Harga</th>
                            <th>Rata Rata Waktu Proses</th>
                            <th>Status</th>
                        </tr>
                    </thead>

                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

<script>
function datatablesInitialize() {
    $('#datatables').DataTable({
        processing: true,
        serverSide: true,
        stateSave: true,
        ordering: false,
        ajax: {
            url: '<?= base_url(uri_string() . '/datatables?userid=' . $this->userid) ?>',
            type: 'POST',
            data: {
                category: $('select[name=category]').val(),
                brand: $('select[name=brand]').val()
            }
        }
    });
}

function loadAverageTimeData() {
    $.ajax({
        url: '<?= base_url(uri_string() . '/average_time?userid=' . $this->userid) ?>',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.status === 'SUCCESS') {
                $('#total-transactions').text(response.data.total_transactions);
                $('#total-time').text(response.data.total_time_formatted);
                $('#average-time').text(response.data.average_time_formatted);
            } else {
                $('#total-transactions').text('-');
                $('#total-time').text('-');
                $('#average-time').text('-');
            }
        },

        error: function() {
            $('#total-transactions').text('-');
            $('#total-time').text('-');
            $('#average-time').text('-');
        }
    });
}

window.onload = function() {
    datatablesInitialize();

    $('select[name=category]').change(function() {
        $.ajax({
            url: '<?= base_url('select/ppob/brand?userid=' . $this->userid) ?>',
            method: 'POST',
            dataType: 'html',
            data: {
                category: $(this).val()
            },
            success: function(response) {
                $('select[name=brand]').html(response);
            }
        }).fail(function() {});
    });
};

function filter() {
    $('#datatables').DataTable().destroy();
    datatablesInitialize();
}
</script>